#!/bin/bash

# STT准确率测试示例脚本
# 演示如何使用STT测试工具进行各种测试场景

set -e

echo "========================================="
echo "STT准确率测试示例"
echo "========================================="

# 检查项目是否已编译
if [ ! -d "target/classes" ]; then
    echo "项目未编译，正在编译..."
    mvn compile -q
fi

# 创建示例音频目录（如果不存在）
if [ ! -d "test_audio" ]; then
    echo "创建示例音频目录..."
    mkdir -p test_audio
    
    # 如果audio目录存在，复制一些文件作为示例
    if [ -d "audio" ]; then
        echo "从audio目录复制示例文件..."
        find audio -name "*.wav" -o -name "*.mp3" -o -name "*.pcm" -o -name "*.opus" | head -5 | while read file; do
            cp "$file" test_audio/ 2>/dev/null || true
        done
    fi
fi

# 检查测试音频文件数量
AUDIO_COUNT=$(find test_audio -type f \( -name "*.wav" -o -name "*.mp3" -o -name "*.pcm" -o -name "*.opus" \) 2>/dev/null | wc -l)
echo "测试音频目录中有 $AUDIO_COUNT 个音频文件"

if [ "$AUDIO_COUNT" -eq 0 ]; then
    echo ""
    echo "警告: 没有找到测试音频文件"
    echo "请将音频文件放入 test_audio 目录中，支持的格式："
    echo "  - .wav (推荐)"
    echo "  - .mp3"
    echo "  - .pcm"
    echo "  - .opus"
    echo ""
    echo "您可以："
    echo "1. 手动复制音频文件到 test_audio 目录"
    echo "2. 使用现有的 audio 目录：./scripts/run_stt_test.sh --audio-dir ./audio --enable-vosk"
    echo ""
    exit 1
fi

# 创建示例期望文本文件
echo "创建示例期望文本文件..."
cat > test_expected_texts.txt << 'EOF'
# STT测试期望文本文件示例
# 格式: 音频文件名<TAB>期望识别文本

# 基础测试
test1.wav	你好世界
test2.mp3	今天天气不错
test3.wav	我想听音乐

# 如果您的音频文件有不同的名称，请相应修改此文件
# 或者让工具从文件名自动推断期望文本
EOF

echo ""
echo "========================================="
echo "示例1: 基本Vosk测试"
echo "========================================="
echo "使用Vosk本地服务测试音频识别准确率"
echo ""

./scripts/run_stt_test.sh \
    --audio-dir test_audio \
    --expected-texts test_expected_texts.txt \
    --enable-vosk \
    --output vosk_test_report.txt

if [ -f "vosk_test_report.txt" ]; then
    echo ""
    echo "Vosk测试完成！查看报告："
    echo "cat vosk_test_report.txt"
    echo ""
fi

echo "========================================="
echo "示例2: 检查可用的云服务配置"
echo "========================================="

# 检查环境变量配置
echo "检查云服务API配置："
AVAILABLE_SERVICES=0

if [ -n "$TENCENT_SECRET_ID" ] && [ -n "$TENCENT_SECRET_KEY" ]; then
    echo "  ✓ 腾讯云STT已配置"
    AVAILABLE_SERVICES=$((AVAILABLE_SERVICES + 1))
else
    echo "  ✗ 腾讯云STT未配置 (需要 TENCENT_SECRET_ID 和 TENCENT_SECRET_KEY)"
fi

if [ -n "$ALIYUN_API_KEY" ]; then
    echo "  ✓ 阿里云STT已配置"
    AVAILABLE_SERVICES=$((AVAILABLE_SERVICES + 1))
else
    echo "  ✗ 阿里云STT未配置 (需要 ALIYUN_API_KEY)"
fi

if [ -n "$XFYUN_APP_ID" ] && [ -n "$XFYUN_API_KEY" ] && [ -n "$XFYUN_API_SECRET" ]; then
    echo "  ✓ 讯飞STT已配置"
    AVAILABLE_SERVICES=$((AVAILABLE_SERVICES + 1))
else
    echo "  ✗ 讯飞STT未配置 (需要 XFYUN_APP_ID, XFYUN_API_KEY, XFYUN_API_SECRET)"
fi

if [ -n "$AZURE_SUBSCRIPTION_KEY" ] && [ -n "$AZURE_REGION" ]; then
    echo "  ✓ Azure STT已配置"
    AVAILABLE_SERVICES=$((AVAILABLE_SERVICES + 1))
else
    echo "  ✗ Azure STT未配置 (需要 AZURE_SUBSCRIPTION_KEY 和 AZURE_REGION)"
fi

if [ -n "$FUNASR_API_URL" ]; then
    echo "  ✓ FunASR已配置"
    AVAILABLE_SERVICES=$((AVAILABLE_SERVICES + 1))
else
    echo "  ✗ FunASR未配置 (需要 FUNASR_API_URL)"
fi

if [ -n "$DOUBAO_APP_ID" ] && [ -n "$DOUBAO_API_KEY" ]; then
    echo "  ✓ 豆包ASR已配置"
    AVAILABLE_SERVICES=$((AVAILABLE_SERVICES + 1))
else
    echo "  ✗ 豆包ASR未配置 (需要 DOUBAO_APP_ID 和 DOUBAO_API_KEY)"
fi

if [ -n "$YOUDAO_APP_ID" ] && [ -n "$YOUDAO_APP_SECRET" ]; then
    echo "  ✓ 有道STT已配置"
    AVAILABLE_SERVICES=$((AVAILABLE_SERVICES + 1))
else
    echo "  ✗ 有道STT未配置 (需要 YOUDAO_APP_ID 和 YOUDAO_APP_SECRET)"
fi

echo ""
echo "可用的云服务数量: $AVAILABLE_SERVICES"

if [ "$AVAILABLE_SERVICES" -gt 0 ]; then
    echo ""
    echo "========================================="
    echo "示例3: 多服务对比测试"
    echo "========================================="
    echo "对比所有可用的STT服务性能"
    echo ""
    
    ./scripts/run_stt_test.sh \
        --audio-dir test_audio \
        --expected-texts test_expected_texts.txt \
        --enable-all \
        --output multi_service_comparison.txt
    
    if [ -f "multi_service_comparison.txt" ]; then
        echo ""
        echo "多服务对比测试完成！查看报告："
        echo "cat multi_service_comparison.txt"
        echo ""
        
        # 显示简要对比结果
        echo "简要对比结果："
        grep -A 20 "性能对比:" multi_service_comparison.txt | head -25 || true
    fi
else
    echo ""
    echo "没有配置云服务API，跳过多服务对比测试"
    echo ""
    echo "要启用云服务测试，请设置相应的环境变量："
    echo "例如，对于腾讯云STT："
    echo "  export TENCENT_SECRET_ID=\"your-secret-id\""
    echo "  export TENCENT_SECRET_KEY=\"your-secret-key\""
    echo ""
fi

echo "========================================="
echo "示例4: 流式识别专项测试"
echo "========================================="
echo "测试支持流式识别的STT服务"
echo ""

./scripts/run_stt_streaming_test.sh \
    --audio-dir test_audio \
    --expected-texts test_expected_texts.txt \
    --enable-vosk \
    --output streaming_test_report.txt

if [ -f "streaming_test_report.txt" ]; then
    echo ""
    echo "流式识别测试完成！查看报告："
    echo "cat streaming_test_report.txt"
    echo ""

    # 显示流式识别优势
    echo "流式识别优势："
    grep -A 10 "流式识别优势分析:" streaming_test_report.txt | head -15 || true
fi

echo ""
echo "========================================="
echo "示例5: 自定义配置测试"
echo "========================================="
echo "演示如何使用自定义配置进行测试"
echo ""

# 运行Java演示程序
java -Xmx2g -Djava.library.path=./lib --enable-preview \
    -cp "target/classes:target/test-classes:target/lib/*" \
    com.xiaozhi.dialogue.stt.SttTestDemo

echo ""
echo "========================================="
echo "测试完成总结"
echo "========================================="
echo ""
echo "生成的报告文件："
ls -la *test_report*.txt 2>/dev/null || echo "  没有找到报告文件"
echo ""
echo "清理临时文件："
echo "  rm -f test_expected_texts.txt"
echo "  rm -f *test_report*.txt"
echo ""
echo "要查看详细的使用说明，请运行："
echo "  ./scripts/run_stt_test.sh --help"
echo ""
echo "要查看完整文档，请查看："
echo "  docs/STT_ACCURACY_TEST.md"
echo ""

# 询问是否清理临时文件
read -p "是否清理临时文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -f test_expected_texts.txt
    echo "临时文件已清理"
else
    echo "保留临时文件"
fi

echo "示例演示完成！"

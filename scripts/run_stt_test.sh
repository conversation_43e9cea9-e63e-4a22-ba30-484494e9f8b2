#!/bin/bash

# STT准确率测试运行脚本
# 使用方法: ./scripts/run_stt_test.sh [选项]

set -e

# 默认配置
AUDIO_DIR="./audio"
OUTPUT_REPORT="./stt_test_report_$(date +%Y%m%d_%H%M%S).txt"
EXPECTED_TEXTS=""
ENABLE_VOSK=false
ENABLE_ALL=false
FORCE_STREAMING=false
JAVA_OPTS="-Xmx2g -Djava.library.path=./lib --enable-preview"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --audio-dir)
            AUDIO_DIR="$2"
            shift 2
            ;;
        --expected-texts)
            EXPECTED_TEXTS="$2"
            shift 2
            ;;
        --output)
            OUTPUT_REPORT="$2"
            shift 2
            ;;
        --enable-vosk)
            ENABLE_VOSK=true
            shift
            ;;
        --enable-all)
            ENABLE_ALL=true
            shift
            ;;
        --force-streaming)
            FORCE_STREAMING=true
            shift
            ;;
        --help|-h)
            echo "STT准确率测试脚本"
            echo ""
            echo "使用方法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --audio-dir <目录>        指定音频文件目录 (默认: ./audio)"
            echo "  --expected-texts <文件>   指定期望文本文件 (可选)"
            echo "  --output <文件>           指定输出报告文件 (默认: 自动生成时间戳文件名)"
            echo "  --enable-vosk             启用Vosk本地识别测试"
            echo "  --enable-all              启用所有可用的STT服务测试"
            echo "  --force-streaming         强制使用流式识别（推荐）"
            echo "  --help, -h                显示此帮助信息"
            echo ""
            echo "环境变量配置 (用于云服务API):"
            echo "  TENCENT_SECRET_ID         腾讯云API密钥ID"
            echo "  TENCENT_SECRET_KEY        腾讯云API密钥"
            echo "  ALIYUN_API_KEY            阿里云API密钥"
            echo "  XFYUN_APP_ID              讯飞应用ID"
            echo "  XFYUN_API_KEY             讯飞API密钥"
            echo "  XFYUN_API_SECRET          讯飞API密钥"
            echo "  AZURE_SUBSCRIPTION_KEY    Azure订阅密钥"
            echo "  AZURE_REGION              Azure区域"
            echo "  FUNASR_API_URL            FunASR API地址"
            echo "  DOUBAO_APP_ID             豆包应用ID"
            echo "  DOUBAO_API_KEY            豆包API密钥"
            echo "  YOUDAO_APP_ID             有道应用ID"
            echo "  YOUDAO_APP_SECRET         有道应用密钥"
            echo ""
            echo "示例:"
            echo "  $0 --audio-dir ./test_audio --enable-vosk"
            echo "  $0 --audio-dir ./test_audio --enable-all --output ./my_report.txt"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 --help 查看使用说明"
            exit 1
            ;;
    esac
done

# 检查音频目录是否存在
if [ ! -d "$AUDIO_DIR" ]; then
    echo "错误: 音频目录不存在: $AUDIO_DIR"
    echo "请使用 --audio-dir 指定正确的音频目录"
    exit 1
fi

# 检查音频文件数量
AUDIO_COUNT=$(find "$AUDIO_DIR" -type f \( -name "*.wav" -o -name "*.mp3" -o -name "*.pcm" -o -name "*.opus" \) | wc -l)
if [ "$AUDIO_COUNT" -eq 0 ]; then
    echo "警告: 在目录 $AUDIO_DIR 中没有找到支持的音频文件 (.wav, .mp3, .pcm, .opus)"
    echo "请确认音频文件存在且格式正确"
fi

echo "找到 $AUDIO_COUNT 个音频文件"

# 检查期望文本文件
if [ -n "$EXPECTED_TEXTS" ] && [ ! -f "$EXPECTED_TEXTS" ]; then
    echo "警告: 期望文本文件不存在: $EXPECTED_TEXTS"
    echo "将尝试从文件名推断期望文本"
    EXPECTED_TEXTS=""
fi

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请确保Java已正确安装"
    exit 1
fi

# 检查项目是否已编译
if [ ! -d "target/classes" ]; then
    echo "项目未编译，正在编译..."
    if command -v mvn &> /dev/null; then
        mvn compile -q
    else
        echo "错误: 未找到Maven，请先编译项目"
        exit 1
    fi
fi

# 构建Java命令参数
JAVA_ARGS=()
JAVA_ARGS+=("--audio-dir" "$AUDIO_DIR")
JAVA_ARGS+=("--output" "$OUTPUT_REPORT")

if [ -n "$EXPECTED_TEXTS" ]; then
    JAVA_ARGS+=("--expected-texts" "$EXPECTED_TEXTS")
fi

if [ "$ENABLE_VOSK" = true ]; then
    JAVA_ARGS+=("--enable-vosk")
fi

if [ "$ENABLE_ALL" = true ]; then
    JAVA_ARGS+=("--enable-all")
fi

if [ "$FORCE_STREAMING" = true ]; then
    JAVA_ARGS+=("--force-streaming")
fi

# 显示配置信息
echo "========================================="
echo "STT准确率测试配置"
echo "========================================="
echo "音频目录: $AUDIO_DIR"
echo "音频文件数量: $AUDIO_COUNT"
echo "期望文本文件: ${EXPECTED_TEXTS:-从文件名推断}"
echo "输出报告: $OUTPUT_REPORT"
echo "启用Vosk: $ENABLE_VOSK"
echo "启用所有服务: $ENABLE_ALL"
echo "强制流式识别: $FORCE_STREAMING"
echo "========================================="

# 检查环境变量配置
echo "检查API配置:"
[ -n "$TENCENT_SECRET_ID" ] && echo "  ✓ 腾讯云STT已配置" || echo "  ✗ 腾讯云STT未配置"
[ -n "$ALIYUN_API_KEY" ] && echo "  ✓ 阿里云STT已配置" || echo "  ✗ 阿里云STT未配置"
[ -n "$XFYUN_APP_ID" ] && echo "  ✓ 讯飞STT已配置" || echo "  ✗ 讯飞STT未配置"
[ -n "$AZURE_SUBSCRIPTION_KEY" ] && echo "  ✓ Azure STT已配置" || echo "  ✗ Azure STT未配置"
[ -n "$FUNASR_API_URL" ] && echo "  ✓ FunASR已配置" || echo "  ✗ FunASR未配置"
[ -n "$DOUBAO_APP_ID" ] && echo "  ✓ 豆包ASR已配置" || echo "  ✗ 豆包ASR未配置"
[ -n "$YOUDAO_APP_ID" ] && echo "  ✓ 有道STT已配置" || echo "  ✗ 有道STT未配置"
echo "========================================="

# 运行测试
echo "开始STT准确率测试..."
echo ""

# 运行测试
java $JAVA_OPTS -cp "target/classes:target/test-classes:target/lib/*" \
    com.xiaozhi.dialogue.stt.SttTestRunner \
    "${JAVA_ARGS[@]}"

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "========================================="
    echo "测试完成！"
    echo "报告文件: $OUTPUT_REPORT"
    echo "========================================="
    
    # 如果报告文件存在，显示简要统计
    if [ -f "$OUTPUT_REPORT" ]; then
        echo ""
        echo "简要统计:"
        grep -E "(测试的STT服务数量|测试音频文件数量)" "$OUTPUT_REPORT" || true
        echo ""
        echo "查看完整报告: cat $OUTPUT_REPORT"
    fi
else
    echo ""
    echo "========================================="
    echo "测试失败！请检查错误信息"
    echo "========================================="
    exit 1
fi

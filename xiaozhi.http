:header = <<
Content-Type: application/json
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36
Authorization: eyJhbGciOiJIUzI1NiJ9.eyJpZCI6MSwidXQiOjEsImV4cCI6MTc2MzUyMjMzOH0.U1Lg4HgBZdAsfoTSfjTFZCNkqLKStcT2YvAo9zirPz0

# Authorization: eyJhbGciOiJIUzI1NiJ9.eyJpZCI6MSwidXQiOjEsImV4cCI6MTc1ODQzODczMX0.TLEiSNj0-jP2EbaYRpQViqC0FC-uulHHd8waguTb3fM


#
:host = https://rdmytikasapitest.lezhilong.cn/xiaozhi-esp32-server/api/v1
# :host = http://localhost:8091/api/v1

#

GET https://rdmytikasapitest.lezhilong.cn/xiaozhi-esp32-server/api/v1/d/medias?category=news|story
Authorization: 90:e5:b1:a9:8c:7c

#
GET :host/m/daily-stats
:header

#
POST :host/m/sms-code
:header

{
  "mobile": "16710245700"
}

#
POST :host/m/login
:header

{
  "mobile": "13957960361",
  "code": "960361"
}

#
POST :host/m/p4t
:header

{
  "device_id": "",
  "action": "open",
  "category": "news"
}

#
POST :host/m/pa
:header

{
  "sence": "早餐"
}


#
GET :host/devices/wakeup?device_id=90:e5:b1:a9:8c:7c
:header

#
PUT :host/ota/version
:header

{
  "version": "1.9.13",
  "url": "https://mytikas-testing.oss-cn-beijing.aliyuncs.com/xiaozhi/xiaozhi.bin"
}

#
POST :host/m/p4t
:header

{
  "device_id": "90:e5:b1:a9:8c:7c",
  "action": "open",
  "category": "news"
}

#
POST :host/medias/gen
:header

{
  "title": "为什么摇晃一袋坚果，大的果仁总会跑到上面来？",
  "category": "story"
}

#
POST :host/medias/gen
:header

{
  "title": "生成今日资讯",
  "category": "news"
}

#
PUT :host/m/tasks/12
:header

{
  "time":"09:33",
  "week_mask":127
}
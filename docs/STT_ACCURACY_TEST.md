# STT准确率测试工具

这是一个用于测试语音识别(STT)服务准确率的工具，支持多种STT服务提供商，可以从指定目录读取音频文件，调用STT服务进行识别，并计算识别准确率。

## 功能特性

- **多服务支持**: 支持Vosk、腾讯云、阿里云、讯飞、Azure、FunASR、豆包、有道等多种STT服务
- **流式识别优先**: 自动检测并优先使用流式识别，更贴近实际应用场景
- **并发处理**: 支持并发处理多个音频文件，提高测试效率
- **准确率计算**: 基于编辑距离算法计算文本相似度，评估识别准确率
- **详细报告**: 生成包含统计信息和详细结果的测试报告
- **灵活配置**: 支持命令行参数和环境变量配置
- **多格式支持**: 支持WAV、MP3、PCM、OPUS等音频格式
- **专项流式测试**: 提供专门的流式识别测试工具

## 目录结构

```
src/test/java/com/xiaozhi/dialogue/stt/
├── SttAccuracyTest.java          # 核心测试类（支持流式识别）
├── SttTestRunner.java            # 测试运行器
├── SttStreamingTest.java         # 专门的流式识别测试类
├── SttStreamingTestRunner.java   # 流式识别测试运行器
├── SttTestDemo.java              # 演示类
└── resources/
    └── expected_texts_example.txt # 期望文本示例文件

scripts/
├── run_stt_test.sh               # 测试运行脚本
├── run_stt_streaming_test.sh     # 流式识别测试脚本
└── stt_test_example.sh           # 示例演示脚本

docs/
└── STT_ACCURACY_TEST.md          # 本文档
```

## 快速开始

### 1. 准备音频文件

将测试音频文件放在指定目录中（默认为 `./audio`），支持的格式：
- `.wav` - WAV格式
- `.mp3` - MP3格式  
- `.pcm` - PCM格式
- `.opus` - OPUS格式

### 2. 准备期望文本文件（可选）

创建一个文本文件，格式为：`音频文件名<TAB>期望识别文本`

示例文件 `expected_texts.txt`：
```
001_你好世界.wav	你好世界
002_今天天气不错.mp3	今天天气不错
003_我想听音乐.wav	我想听音乐
```

如果不提供期望文本文件，工具会尝试从文件名中推断期望文本。

### 3. 运行测试

#### 使用脚本运行（推荐）

```bash
# 基本测试（仅Vosk本地服务，自动使用流式识别）
./scripts/run_stt_test.sh --enable-vosk

# 测试所有可用服务（优先使用流式识别）
./scripts/run_stt_test.sh --enable-all

# 强制使用流式识别（推荐）
./scripts/run_stt_test.sh --enable-all --force-streaming

# 专门的流式识别测试
./scripts/run_stt_streaming_test.sh --enable-all

# 指定音频目录和期望文本文件
./scripts/run_stt_test.sh --audio-dir ./test_audio --expected-texts ./expected_texts.txt --enable-vosk

# 自定义输出报告文件
./scripts/run_stt_test.sh --audio-dir ./test_audio --output ./my_report.txt --enable-all
```

#### 直接使用Java运行

```bash
# 编译项目
mvn compile

# 运行测试
java -Xmx2g -Djava.library.path=./lib --enable-preview \
  -cp "target/classes:target/lib/*" \
  com.xiaozhi.dialogue.stt.SttTestRunner \
  --audio-dir ./audio --enable-vosk
```

## 配置说明

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--audio-dir` | 音频文件目录 | `./audio` |
| `--expected-texts` | 期望文本文件路径 | 无（从文件名推断） |
| `--output` | 输出报告文件路径 | `./stt_test_report.txt` |
| `--enable-vosk` | 启用Vosk本地识别测试 | 否 |
| `--enable-all` | 启用所有可用的STT服务测试 | 否 |

### 环境变量配置

为了使用云服务API，需要设置相应的环境变量：

#### 腾讯云STT
```bash
export TENCENT_SECRET_ID="your-secret-id"
export TENCENT_SECRET_KEY="your-secret-key"
```

#### 阿里云STT
```bash
export ALIYUN_API_KEY="your-api-key"
```

#### 讯飞STT
```bash
export XFYUN_APP_ID="your-app-id"
export XFYUN_API_KEY="your-api-key"
export XFYUN_API_SECRET="your-api-secret"
```

#### Azure STT
```bash
export AZURE_SUBSCRIPTION_KEY="your-subscription-key"
export AZURE_REGION="your-region"
```

#### FunASR
```bash
export FUNASR_API_URL="your-api-url"
```

#### 豆包ASR
```bash
export DOUBAO_APP_ID="your-app-id"
export DOUBAO_API_KEY="your-api-key"
```

#### 有道STT
```bash
export YOUDAO_APP_ID="your-app-id"
export YOUDAO_APP_SECRET="your-app-secret"
```

## 测试报告

测试完成后会生成详细的测试报告，包含以下内容：

### 总体统计
- 测试的STT服务数量
- 测试音频文件数量

### 各服务详细结果
- 总文件数
- 成功识别数量
- 识别失败数量
- 成功率
- 平均准确率
- 平均处理时间
- 每个文件的详细识别结果

### 性能对比表
对比各个STT服务的性能指标

## 流式识别 vs 批量识别

### 流式识别的优势

1. **实时性**: 可以在音频播放过程中实时返回识别结果
2. **内存效率**: 不需要缓存完整音频文件，适合长音频处理
3. **用户体验**: 用户可以更快看到识别结果，提升交互体验
4. **网络优化**: 可以边传输边处理，减少网络延迟影响

### 支持情况

| STT服务 | 批量识别 | 流式识别 | 推荐方式 |
|---------|----------|----------|----------|
| Vosk | ✅ | ✅ | 流式识别 |
| 腾讯云STT | ✅ | ✅ | 流式识别 |
| 阿里云STT | ❌ | ✅ | 流式识别 |
| 讯飞STT | ✅ | ✅ | 流式识别 |
| Azure STT | ✅ | ✅ | 流式识别 |
| FunASR | ❌ | ✅ | 流式识别 |
| 豆包ASR | ❌ | ✅ | 流式识别 |
| 有道STT | ❌ | ✅ | 流式识别 |

### 使用建议

- **优先使用流式识别**: 大多数现代STT服务都支持流式识别，且性能更好
- **测试真实场景**: 流式识别更接近实际应用场景
- **注意连接稳定性**: 流式识别对网络稳定性要求较高

## 准确率计算方法

工具使用以下方法计算识别准确率：

1. **文本预处理**: 移除空格和标点符号，只比较文字内容
2. **编辑距离**: 使用Levenshtein距离算法计算文本差异
3. **相似度计算**: `相似度 = 1 - (编辑距离 / 最大文本长度)`
4. **平均准确率**: 所有成功识别文件的相似度平均值

## 使用示例

### 示例1：测试Vosk本地服务

```bash
# 准备音频文件
mkdir -p test_audio
cp your_audio_files/* test_audio/

# 运行测试
./scripts/run_stt_test.sh --audio-dir test_audio --enable-vosk
```

### 示例2：对比多个STT服务

```bash
# 设置API密钥
export TENCENT_SECRET_ID="your-tencent-id"
export TENCENT_SECRET_KEY="your-tencent-key"
export ALIYUN_API_KEY="your-aliyun-key"

# 运行对比测试
./scripts/run_stt_test.sh --audio-dir test_audio --enable-all --output comparison_report.txt
```

### 示例3：使用期望文本文件

```bash
# 创建期望文本文件
cat > expected_texts.txt << EOF
test1.wav	这是第一个测试音频
test2.mp3	这是第二个测试音频
test3.wav	这是第三个测试音频
EOF

# 运行测试
./scripts/run_stt_test.sh --audio-dir test_audio --expected-texts expected_texts.txt --enable-vosk
```

## 注意事项

1. **音频格式**: 确保音频文件格式正确，推荐使用16kHz采样率的单声道音频
2. **文件命名**: 如果不使用期望文本文件，建议音频文件名包含期望的识别文本
3. **API配额**: 使用云服务时注意API调用配额和费用
4. **并发限制**: 某些云服务可能有并发请求限制，可以调整线程池大小
5. **网络环境**: 云服务测试需要稳定的网络连接

## 故障排除

### 常见问题

1. **找不到音频文件**
   - 检查音频目录路径是否正确
   - 确认音频文件格式是否支持

2. **Vosk模型加载失败**
   - 检查 `models/vosk-model` 目录是否存在
   - 确认模型文件完整性

3. **云服务API调用失败**
   - 检查环境变量配置是否正确
   - 确认API密钥有效性和权限
   - 检查网络连接

4. **内存不足**
   - 增加JVM内存参数：`-Xmx4g`
   - 减少并发线程数

### 日志调试

可以通过以下方式启用详细日志：

```bash
java -Dlogging.level.com.xiaozhi.dialogue.stt=DEBUG \
  -cp "target/classes:target/lib/*" \
  com.xiaozhi.dialogue.stt.SttTestRunner \
  --audio-dir ./audio --enable-vosk
```

## 扩展开发

### 添加新的STT服务

1. 实现 `SttService` 接口
2. 在 `SttServiceFactory` 中添加服务创建逻辑
3. 在 `SttTestRunner` 中添加配置支持

### 自定义准确率算法

可以修改 `SttAccuracyTest.calculateTextSimilarity()` 方法来实现自定义的准确率计算算法。

### 添加新的音频格式支持

在 `AudioUtils` 类中添加新格式的转换方法，并更新 `SUPPORTED_FORMATS` 集合。

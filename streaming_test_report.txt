STT流式识别测试报告
生成时间: 2025-10-10 14:57:48
=================================================================================

总体统计:
----------------------------------------
测试的STT服务数量: 1
测试音频文件数量: 5

各服务流式识别详细结果:
=================================================================================

【vosk】
----------------------------------------
总文件数: 5
支持流式识别: 1
流式识别成功: 1
流式识别失败: 0
流式支持率: 20.00%
流式成功率: 100.00%
平均准确率: 0.00%
平均处理时间: 6357.40 ms

详细结果:
  文件: 2c28a4d3a70248b5877ce92ab87eaad1.mp3
    期望: null
    识别: null
    状态: 失败
    使用流式: 否
    音频块数: 0
    平均块处理时间: 0.00 ms
    总耗时: 0 ms
    错误: 使用ffmpeg转换MP3失败: Cannot run program "ffmpeg": error=2, No such file or directory

  文件: 4bb95398c8734d7b83bb28f2b62221c0.mp3
    期望: null
    识别: null
    状态: 失败
    使用流式: 否
    音频块数: 0
    平均块处理时间: 0.00 ms
    总耗时: 0 ms
    错误: 使用ffmpeg转换MP3失败: Cannot run program "ffmpeg": error=2, No such file or directory

  文件: 4ce2f8fff8054b8a91f447ba28597d19.mp3
    期望: null
    识别: null
    状态: 失败
    使用流式: 否
    音频块数: 0
    平均块处理时间: 0.00 ms
    总耗时: 0 ms
    错误: 使用ffmpeg转换MP3失败: Cannot run program "ffmpeg": error=2, No such file or directory

  文件: 955e78093b6d4d72994dfd6b529400f0.mp3
    期望: null
    识别: null
    状态: 失败
    使用流式: 否
    音频块数: 0
    平均块处理时间: 0.00 ms
    总耗时: 0 ms
    错误: 使用ffmpeg转换MP3失败: Cannot run program "ffmpeg": error=2, No such file or directory

  文件: 我在我的。我怎么没音儿啊？什么玩意儿？.wav
    期望: 
    识别: 我怎么没人啊
    状态: 成功
    使用流式: 是
    音频块数: 270
    平均块处理时间: 117.73 ms
    总耗时: 31787 ms


流式识别性能对比:
=================================================================================
服务              支持率        成功率          平均准确率           平均处理时间          总处理文件          
------------------------------------------------------------------------------------------
vosk            20.00      100.00       0.00            6357.40         5              


流式识别优势分析:
=================================================================================
1. 实时性: 流式识别可以在音频播放过程中实时返回识别结果
2. 内存效率: 不需要缓存完整音频文件，适合长音频处理
3. 用户体验: 用户可以更快看到识别结果，提升交互体验
4. 网络优化: 可以边传输边处理，减少网络延迟影响

注意事项:
- 流式识别对网络稳定性要求较高
- 某些服务可能对并发流式连接有限制
- 流式识别的准确率可能略低于批量识别

# STT准确率测试工具 - 实现总结（更新版）

## 概述

已成功为小智ESP32服务器项目创建了完整的STT（语音转文本）准确率测试工具。该工具可以从指定目录读取音频文件，调用各种STT服务进行识别，并计算识别准确率。

**重要更新**: 针对很多STT服务的`recognition`方法未实现的问题，工具已优化为**优先使用流式识别**，更贴近实际应用场景。

## 实现的功能

### 1. 核心测试类
- **SttAccuracyTest.java**: 主要的测试逻辑类
  - 支持多种音频格式（WAV、MP3、PCM、OPUS）
  - **智能识别方式选择**: 优先使用流式识别，回退到批量识别
  - 并发处理音频文件，提高测试效率
  - 基于编辑距离算法计算文本相似度
  - 生成详细的测试统计信息

### 2. 流式识别专项测试
- **SttStreamingTest.java**: 专门的流式识别测试类
  - 模拟真实的流式音频传输场景
  - 分块发送音频数据（100ms/块）
  - 详细的流式识别性能分析
  - 避免并发流式连接冲突

### 3. 测试运行器
- **SttTestRunner.java**: 通用测试运行器
  - 支持多种STT服务配置
  - 自动检测并使用流式识别
  - 通过环境变量配置API密钥
  - 生成详细的测试报告

- **SttStreamingTestRunner.java**: 流式识别专用运行器
  - 专门测试流式识别功能
  - 顺序处理避免连接冲突
  - 生成流式识别专项报告

### 4. 演示和示例
- **SttTestDemo.java**: 演示如何使用测试工具
- **expected_texts_example.txt**: 期望文本文件示例
- **run_stt_test.sh**: 通用测试运行脚本
- **run_stt_streaming_test.sh**: 流式识别专用脚本
- **stt_test_example.sh**: 完整的使用示例脚本

### 4. 文档
- **STT_ACCURACY_TEST.md**: 详细的使用文档
- **STT_TEST_SUMMARY.md**: 本总结文档

## 支持的STT服务

1. **Vosk** - 本地离线识别服务
2. **腾讯云STT** - 腾讯云语音识别
3. **阿里云STT** - 阿里云语音识别
4. **讯飞STT** - 科大讯飞语音识别
5. **Azure STT** - 微软Azure语音识别
6. **FunASR** - 阿里达摩院FunASR
7. **豆包ASR** - 字节跳动豆包语音识别
8. **有道STT** - 网易有道语音识别

## 主要特性

### 1. 流式识别优先
- **智能识别方式选择**: 自动检测并优先使用流式识别
- **智能回退**: 不支持流式识别的服务自动回退到批量识别
- **真实场景模拟**: 流式识别更贴近实际应用场景
- **专项流式测试**: 提供专门的流式识别测试工具

### 2. 多格式音频支持
- WAV格式（推荐）
- MP3格式
- PCM格式
- OPUS格式

### 3. 灵活的期望文本配置
- 支持外部期望文本文件
- 支持从文件名自动推断期望文本
- 支持注释和空行

### 4. 准确率计算
- 基于Levenshtein编辑距离算法
- 自动去除空格和标点符号
- 计算文本相似度百分比

### 5. 并发处理
- 使用线程池并发处理音频文件
- 可配置并发线程数
- 提高测试效率
- 流式识别采用顺序处理避免连接冲突

### 6. 详细报告
- 总体统计信息
- 各服务详细结果
- 性能对比表
- 每个文件的详细识别结果
- 流式识别专项分析

## 流式识别优势

1. **实时性**: 可以在音频播放过程中实时返回识别结果
2. **内存效率**: 不需要缓存完整音频文件，适合长音频处理
3. **用户体验**: 用户可以更快看到识别结果，提升交互体验
4. **网络优化**: 可以边传输边处理，减少网络延迟影响

## STT服务支持情况

| STT服务 | 批量识别 | 流式识别 | 推荐方式 | 备注 |
|---------|----------|----------|----------|------|
| Vosk | ✅ | ✅ | 流式识别 | 本地离线服务 |
| 腾讯云STT | ✅ | ✅ | 流式识别 | 云服务 |
| 阿里云STT | ❌ | ✅ | 流式识别 | 仅支持流式 |
| 讯飞STT | ✅ | ✅ | 流式识别 | 云服务 |
| Azure STT | ✅ | ✅ | 流式识别 | 微软云服务 |
| FunASR | ❌ | ✅ | 流式识别 | 仅支持流式 |
| 豆包ASR | ❌ | ✅ | 流式识别 | 仅支持流式 |
| 有道STT | ❌ | ✅ | 流式识别 | 仅支持流式 |

## 使用方法

### 1. 快速开始
```bash
# 基本测试（仅Vosk，自动使用流式识别）
./scripts/run_stt_test.sh --enable-vosk

# 测试所有可用服务（优先流式识别）
./scripts/run_stt_test.sh --enable-all

# 专门的流式识别测试（推荐）
./scripts/run_stt_streaming_test.sh --enable-all

# 运行完整示例
./scripts/stt_test_example.sh
```

### 2. 自定义配置
```bash
# 指定音频目录和期望文本文件
./scripts/run_stt_test.sh \
  --audio-dir ./test_audio \
  --expected-texts ./expected_texts.txt \
  --enable-all \
  --output ./my_report.txt
```

### 3. 环境变量配置
```bash
# 腾讯云STT
export TENCENT_SECRET_ID="your-secret-id"
export TENCENT_SECRET_KEY="your-secret-key"

# 阿里云STT
export ALIYUN_API_KEY="your-api-key"

# 其他服务类似...
```

## 文件结构

```
src/test/java/com/xiaozhi/dialogue/stt/
├── SttAccuracyTest.java          # 核心测试类
├── SttTestRunner.java            # 测试运行器
└── SttTestDemo.java              # 演示类

src/test/resources/
└── expected_texts_example.txt    # 期望文本示例

scripts/
├── run_stt_test.sh              # 测试运行脚本
└── stt_test_example.sh          # 示例演示脚本

docs/
└── STT_ACCURACY_TEST.md         # 详细文档
```

## 测试报告示例

测试完成后会生成包含以下内容的报告：

```
STT准确率测试报告
生成时间: 2025-10-07 15:30:00
================================================================================

总体统计:
----------------------------------------
测试的STT服务数量: 3
测试音频文件数量: 10

各服务详细结果:
================================================================================

【vosk】
----------------------------------------
总文件数: 10
成功识别: 8
识别失败: 2
成功率: 80.00%
平均准确率: 85.50%
平均处理时间: 1250.00 ms

性能对比:
================================================================================
服务            成功率     平均准确率   平均处理时间    总处理文件
--------------------------------------------------------------------------------
vosk           80.00      85.50        1250.00         10
tencent        90.00      92.30        800.00          10
aliyun         85.00      88.75        950.00          10
```

## 技术实现细节

### 1. 准确率计算算法
- 使用Levenshtein距离算法计算编辑距离
- 相似度 = 1 - (编辑距离 / 最大文本长度)
- 支持自定义准确率计算方法

### 2. 并发处理机制
- 使用CompletableFuture实现异步处理
- 固定大小线程池（默认4个线程）
- 避免阻塞主线程

### 3. 音频格式处理
- 集成AudioUtils工具类
- 自动转换音频格式为PCM
- 支持多种输入格式

### 4. 错误处理
- 完善的异常处理机制
- 详细的错误信息记录
- 优雅的失败处理

## 扩展性

### 1. 添加新的STT服务
1. 实现SttService接口
2. 在SttServiceFactory中添加创建逻辑
3. 在SttTestRunner中添加配置支持

### 2. 自定义准确率算法
可以修改`calculateTextSimilarity`方法实现自定义算法，如：
- 基于语义的相似度计算
- 关键词匹配算法
- 基于音素的相似度计算

### 3. 添加新的音频格式
在AudioUtils中添加新格式的转换方法，并更新支持的格式列表。

## 依赖要求

### 1. Maven依赖
- Spring Boot Test Starter
- JUnit Jupiter
- Mockito Core

### 2. 系统要求
- Java 21+
- Maven 3.6+
- FFmpeg（用于音频格式转换）

### 3. 可选依赖
- Vosk模型文件（本地识别）
- 各云服务的API密钥

## 使用建议

1. **音频质量**: 使用16kHz采样率的单声道音频获得最佳效果
2. **文件命名**: 建议音频文件名包含期望的识别文本
3. **API配额**: 注意云服务的API调用限制和费用
4. **网络环境**: 确保稳定的网络连接用于云服务测试
5. **并发控制**: 根据服务限制调整并发线程数

## 总结

该STT准确率测试工具提供了完整的语音识别服务测试解决方案，支持多种服务提供商，具有良好的扩展性和易用性。通过详细的测试报告和性能对比，可以帮助选择最适合的STT服务，并评估识别准确率。

工具已经过编译测试，可以直接使用。建议先运行示例脚本熟悉功能，然后根据实际需求进行配置和使用。

package com.xiaozhi.dialogue.stt;

import com.xiaozhi.entity.SysConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * STT测试演示类
 * 展示如何使用STT准确率测试工具
 */
public class SttTestDemo {
    
    private static final Logger logger = LoggerFactory.getLogger(SttTestDemo.class);

    public static void main(String[] args) {
        logger.info("STT准确率测试演示");

        // 直接创建测试实例
        SttAccuracyTest test = new SttAccuracyTest();

        // 演示1: 基本使用
        basicUsageDemo(test);

        // 演示2: 多服务对比
        // multiServiceComparisonDemo(test);

        // 演示3: 自定义配置
        // customConfigurationDemo(test);
    }
    
    /**
     * 演示1: 基本使用
     */
    private static void basicUsageDemo(SttAccuracyTest test) {
        logger.info("========== 演示1: 基本使用 ==========");

        try {
            // 使用注入的测试实例
            
            var configs = new ArrayList<SysConfig>();
            var aliyunConfig = new SysConfig()
                .setProvider("aliyun")
                .setName("paraformer-realtime-v2")
                .setType("stt")
                    .setAppId("sk-98d57d03e5f14b8eb99d1867307da6e5")
                    .setApiKey("sk-98d57d03e5f14b8eb99d1867307da6e5")
                    .setApiSecret("sk-98d57d03e5f14b8eb99d1867307da6e5");
            aliyunConfig.setId(4);
            configs.add(aliyunConfig);
            
            // 运行测试
            String audioDirectory = "/Users/<USER>/Downloads/uwav";
            var results = test.runAccuracyTest(audioDirectory, null, configs);
            
            // 输出结果
            printResults("基本使用演示", results);
            
        } catch (Exception e) {
            logger.error("基本使用演示失败", e);
        }
    }
    
    /**
     * 演示2: 多服务对比
     */
    private static void multiServiceComparisonDemo(SttAccuracyTest test) {
        logger.info("========== 演示2: 多服务对比 ==========");

        try {
            // 使用注入的测试实例
            
            // 创建多个STT服务配置
            List<SysConfig> configs = createMultipleConfigs();
            
            if (configs.isEmpty()) {
                logger.warn("没有可用的STT服务配置，跳过多服务对比演示");
                return;
            }
            
            // 运行测试
            String audioDirectory = "./audio";
            String expectedTextsFile = "./src/test/resources/expected_texts_example.txt";
            Map<String, SttAccuracyTest.AccuracyStats> results = test.runAccuracyTest(
                audioDirectory, expectedTextsFile, configs);
            
            // 输出结果
            printResults("多服务对比演示", results);
            
            // 输出性能对比
            printPerformanceComparison(results);
            
        } catch (Exception e) {
            logger.error("多服务对比演示失败", e);
        }
    }
    
    /**
     * 演示3: 自定义配置
     */
    private static void customConfigurationDemo(SttAccuracyTest test) {
        logger.info("========== 演示3: 自定义配置 ==========");

        try {
            // 使用注入的测试实例
            
            // 创建自定义配置
            List<SysConfig> configs = new ArrayList<>();
            
            // 添加Vosk配置
            SysConfig voskConfig = new SysConfig()
                .setProvider("vosk")
                .setName("Vosk自定义配置")
                .setType("stt");
            voskConfig.setId(-1);
            configs.add(voskConfig);
            
            // 如果有腾讯云配置，添加腾讯云STT
            String tencentSecretId = System.getenv("TENCENT_SECRET_ID");
            String tencentSecretKey = System.getenv("TENCENT_SECRET_KEY");
            if (tencentSecretId != null && tencentSecretKey != null) {
                SysConfig tencentConfig = new SysConfig()
                    .setProvider("tencent")
                    .setName("腾讯云STT自定义")
                    .setType("stt")
                    .setApiKey(tencentSecretId)
                    .setApiSecret(tencentSecretKey)
                    .setApiUrl("https://asr.tencentcloudapi.com/");
                tencentConfig.setId(1);
                configs.add(tencentConfig);
                logger.info("添加腾讯云STT配置");
            }
            
            // 运行测试
            String audioDirectory = "./audio";
            Map<String, SttAccuracyTest.AccuracyStats> results = test.runAccuracyTest(
                audioDirectory, null, configs);
            
            // 输出结果
            printResults("自定义配置演示", results);
            
        } catch (Exception e) {
            logger.error("自定义配置演示失败", e);
        }
    }
    
    /**
     * 创建多个STT服务配置
     */
    private static List<SysConfig> createMultipleConfigs() {
        List<SysConfig> configs = new ArrayList<>();
        
        // Vosk本地服务
        SysConfig voskConfig = new SysConfig()
            .setProvider("vosk")
            .setName("Vosk本地识别")
            .setType("stt");
        voskConfig.setId(-1);
        configs.add(voskConfig);
        
        // 腾讯云STT
        String tencentSecretId = System.getenv("TENCENT_SECRET_ID");
        String tencentSecretKey = System.getenv("TENCENT_SECRET_KEY");
        if (tencentSecretId != null && tencentSecretKey != null) {
            SysConfig tencentConfig = new SysConfig()
                .setProvider("tencent")
                .setName("腾讯云STT")
                .setType("stt")
                .setApiKey(tencentSecretId)
                .setApiSecret(tencentSecretKey)
                .setApiUrl("https://asr.tencentcloudapi.com/");
            tencentConfig.setId(1);
            configs.add(tencentConfig);
        }
        
        // 阿里云STT
        String aliyunApiKey = System.getenv("ALIYUN_API_KEY");
        if (aliyunApiKey != null) {
            SysConfig aliyunConfig = new SysConfig()
                .setProvider("aliyun")
                .setName("阿里云STT")
                .setType("stt")
                .setApiKey(aliyunApiKey);
            aliyunConfig.setId(2);
            configs.add(aliyunConfig);
        }
        
        // 讯飞STT
        String xfyunAppId = System.getenv("XFYUN_APP_ID");
        String xfyunApiKey = System.getenv("XFYUN_API_KEY");
        String xfyunApiSecret = System.getenv("XFYUN_API_SECRET");
        if (xfyunAppId != null && xfyunApiKey != null && xfyunApiSecret != null) {
            SysConfig xfyunConfig = new SysConfig()
                .setProvider("xfyun")
                .setName("讯飞STT")
                .setType("stt")
                .setAppId(xfyunAppId)
                .setApiKey(xfyunApiKey)
                .setApiSecret(xfyunApiSecret);
            xfyunConfig.setId(3);
            configs.add(xfyunConfig);
        }
        
        return configs;
    }
    
    /**
     * 打印测试结果
     */
    private static void printResults(String title, Map<String, SttAccuracyTest.AccuracyStats> results) {
        logger.info("========== {} 结果 ==========", title);
        
        for (Map.Entry<String, SttAccuracyTest.AccuracyStats> entry : results.entrySet()) {
            String provider = entry.getKey();
            SttAccuracyTest.AccuracyStats stats = entry.getValue();
            
            logger.info("STT服务: {}", provider);
            logger.info("  总文件数: {}", stats.getTotalFiles());
            logger.info("  成功识别: {}", stats.getSuccessfulRecognitions());
            logger.info("  识别失败: {}", stats.getFailedRecognitions());
            logger.info("  成功率: {}%", stats.getSuccessRate());
            logger.info("  平均准确率: {}%", stats.getAverageAccuracy() * 100);
            logger.info("  平均处理时间: {}ms", stats.getAverageProcessingTime());
            logger.info("");
        }
    }
    
    /**
     * 打印性能对比
     */
    private static void printPerformanceComparison(Map<String, SttAccuracyTest.AccuracyStats> results) {
        logger.info("========== 性能对比 ==========");
        logger.info(String.format("%-15s %-10s %-12s %-15s", 
            "服务", "成功率", "平均准确率", "平均处理时间"));
        logger.info("-".repeat(60));
        
        for (Map.Entry<String, SttAccuracyTest.AccuracyStats> entry : results.entrySet()) {
            String provider = entry.getKey();
            SttAccuracyTest.AccuracyStats stats = entry.getValue();
            
            logger.info(String.format("%-15s %-10.2f %-12.2f %-15.2f",
                provider,
                stats.getSuccessRate(),
                stats.getAverageAccuracy() * 100,
                stats.getAverageProcessingTime()));
        }
        logger.info("=" + "=".repeat(60));
    }
    
    /**
     * 演示如何创建自定义的准确率计算方法
     */
    public static class CustomAccuracyCalculator {
        
        /**
         * 自定义文本相似度计算方法
         * 这里可以实现更复杂的算法，如基于语义的相似度计算
         */
        public static double calculateCustomSimilarity(String expected, String recognized) {
            if (expected == null || recognized == null) return 0.0;
            if (expected.isEmpty() && recognized.isEmpty()) return 1.0;
            if (expected.isEmpty() || recognized.isEmpty()) return 0.0;
            
            // 示例：基于关键词匹配的相似度计算
            String[] expectedWords = expected.split("\\s+");
            String[] recognizedWords = recognized.split("\\s+");
            
            int matchCount = 0;
            for (String expectedWord : expectedWords) {
                for (String recognizedWord : recognizedWords) {
                    if (expectedWord.equals(recognizedWord)) {
                        matchCount++;
                        break;
                    }
                }
            }
            
            return (double) matchCount / Math.max(expectedWords.length, recognizedWords.length);
        }
    }
}

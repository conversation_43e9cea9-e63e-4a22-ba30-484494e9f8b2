package com.xiaozhi.dialogue.stt;

import com.xiaozhi.dialogue.stt.factory.SttServiceFactory;
import com.xiaozhi.dialogue.stt.SttService;
import com.xiaozhi.entity.SysConfig;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * STT服务传统单元测试
 * 不依赖Spring框架，直接实例化和测试STT服务
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class SttUnitTest {
    
    private static final Logger logger = LoggerFactory.getLogger(SttUnitTest.class);
    
    private SttServiceFactory sttServiceFactory;
    
    @BeforeAll
    public void setUp() {
        logger.info("初始化STT服务工厂...");
        
        // 直接实例化SttServiceFactory
        sttServiceFactory = new SttServiceFactory();
        
        // 手动调用初始化方法
        sttServiceFactory.initializeDefaultSttService();
        
        logger.info("STT服务工厂初始化完成");
    }
    
    @Test
    public void testVoskService() {
        logger.info("测试Vosk STT服务...");
        
        try {
            // 创建Vosk配置
            SysConfig voskConfig = new SysConfig();
            voskConfig.setProvider("vosk");
            voskConfig.setId(1);
            
            // 获取Vosk服务
            SttService voskService = sttServiceFactory.getSttService(voskConfig);
            logger.info("Vosk服务获取成功: {}", voskService.getClass().getSimpleName());
            
            // 测试音频文件
            String audioDir = "/Users/<USER>/Downloads/uwav";
            File dir = new File(audioDir);
            if (!dir.exists()) {
                logger.warn("音频目录不存在: {}", audioDir);
                return;
            }
            
            File[] audioFiles = dir.listFiles((d, name) -> 
                name.toLowerCase().endsWith(".wav") || 
                name.toLowerCase().endsWith(".pcm"));
            
            if (audioFiles == null || audioFiles.length == 0) {
                logger.warn("未找到音频文件");
                return;
            }
            
            logger.info("找到 {} 个音频文件", audioFiles.length);
            
            // 测试第一个音频文件
            File audioFile = audioFiles[0];
            logger.info("测试音频文件: {}", audioFile.getName());
            
            byte[] audioData = Files.readAllBytes(audioFile.toPath());
            logger.info("音频数据大小: {} bytes", audioData.length);
            
            // 调用识别方法
            String result = voskService.recognition(audioData);
            logger.info("Vosk识别结果: {}", result);
            
            if (result != null && !result.trim().isEmpty()) {
                logger.info("✅ Vosk服务工作正常");
            } else {
                logger.warn("⚠️ Vosk服务返回空结果");
            }
            
        } catch (Exception e) {
            logger.error("❌ Vosk服务测试失败", e);
        }
    }
    
    @Test
    public void testAliyunService() {
        logger.info("测试阿里云STT服务...");
        
        try {
            // 创建阿里云配置
            SysConfig aliyunConfig = new SysConfig();
            aliyunConfig.setProvider("aliyun");
            aliyunConfig.setId(2);
            
            // 获取阿里云服务
            SttService aliyunService = sttServiceFactory.getSttService(aliyunConfig);
            logger.info("阿里云服务获取成功: {}", aliyunService.getClass().getSimpleName());
            
            // 测试音频文件
            String audioDir = "/Users/<USER>/Downloads/uwav";
            File dir = new File(audioDir);
            if (!dir.exists()) {
                logger.warn("音频目录不存在: {}", audioDir);
                return;
            }
            
            File[] audioFiles = dir.listFiles((d, name) -> 
                name.toLowerCase().endsWith(".wav") || 
                name.toLowerCase().endsWith(".pcm"));
            
            if (audioFiles == null || audioFiles.length == 0) {
                logger.warn("未找到音频文件");
                return;
            }
            
            logger.info("找到 {} 个音频文件", audioFiles.length);
            
            // 测试第一个音频文件
            File audioFile = audioFiles[0];
            logger.info("测试音频文件: {}", audioFile.getName());
            
            byte[] audioData = Files.readAllBytes(audioFile.toPath());
            logger.info("音频数据大小: {} bytes", audioData.length);
            
            // 测试流式识别
            if (aliyunService.supportsStreaming()) {
                logger.info("测试阿里云流式识别...");
                
                // 创建音频数据流
                reactor.core.publisher.Sinks.Many<byte[]> audioSink = 
                    reactor.core.publisher.Sinks.many().multicast().onBackpressureBuffer();
                
                // 异步调用流式识别
                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                    return aliyunService.streamRecognition(audioSink);
                });
                
                // 模拟流式发送音频数据
                new Thread(() -> {
                    try {
                        Thread.sleep(500); // 等待连接建立
                        
                        int chunkSize = 1600; // 100ms音频数据
                        for (int i = 0; i < audioData.length; i += chunkSize) {
                            int end = Math.min(i + chunkSize, audioData.length);
                            byte[] chunk = new byte[end - i];
                            System.arraycopy(audioData, i, chunk, 0, end - i);
                            
                            audioSink.tryEmitNext(chunk);
                            Thread.sleep(50); // 模拟实时音频流
                        }
                        
                        audioSink.tryEmitComplete();
                        
                    } catch (Exception e) {
                        logger.error("发送音频数据失败", e);
                        audioSink.tryEmitError(e);
                    }
                }).start();
                
                // 等待识别结果
                String result = future.get(30, TimeUnit.SECONDS);
                logger.info("阿里云流式识别结果: {}", result);
                
                if (result != null && !result.trim().isEmpty()) {
                    logger.info("✅ 阿里云流式识别服务工作正常");
                } else {
                    logger.warn("⚠️ 阿里云流式识别服务返回空结果");
                }
            } else {
                // 测试批量识别
                String result = aliyunService.recognition(audioData);
                logger.info("阿里云批量识别结果: {}", result);
                
                if (result != null && !result.trim().isEmpty()) {
                    logger.info("✅ 阿里云批量识别服务工作正常");
                } else {
                    logger.warn("⚠️ 阿里云批量识别服务返回空结果");
                }
            }
            
        } catch (Exception e) {
            logger.error("❌ 阿里云服务测试失败", e);
        }
    }
    
    @Test
    public void testMultipleServices() {
        logger.info("测试多个STT服务...");
        
        String[] providers = {"vosk", "aliyun", "tencent", "xfyun"};
        
        for (int i = 0; i < providers.length; i++) {
            try {
                String provider = providers[i];
                logger.info("测试STT服务: {}", provider);
                
                SysConfig config = new SysConfig();
                config.setProvider(provider);
                config.setId(i + 1);
                
                SttService service = sttServiceFactory.getSttService(config);
                logger.info("✅ {} 服务创建成功: {}", provider, service.getClass().getSimpleName());
                
                // 检查是否支持流式识别
                boolean supportsStreaming = service.supportsStreaming();
                logger.info("  支持流式识别: {}", supportsStreaming);
                
            } catch (Exception e) {
                logger.warn("⚠️ {} 服务创建失败: {}", providers[i], e.getMessage());
            }
        }
    }
    
    public static void main(String[] args) {
        logger.info("开始STT单元测试...");
        
        SttUnitTest test = new SttUnitTest();
        test.setUp();
        
        // 运行测试
        test.testVoskService();
        test.testAliyunService();
        test.testMultipleServices();
        
        logger.info("STT单元测试完成");
    }
}

package com.xiaozhi.dialogue.stt;

import com.xiaozhi.dialogue.stt.factory.SttServiceFactory;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.utils.AudioUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import reactor.core.publisher.Sinks;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * STT服务准确率测试类
 * 从指定目录读取音频文件，调用STT服务识别文本，计算识别准确率
 * 传统单元测试方式，不依赖Spring框架
 */
public class SttAccuracyTest {

    private static final Logger logger = LoggerFactory.getLogger(SttAccuracyTest.class);

    private final SttServiceFactory sttServiceFactory;

    // 支持的音频文件格式
    private static final Set<String> SUPPORTED_FORMATS = Set.of(".wav", ".mp3", ".pcm", ".opus");

    // 测试结果统计
    public static class TestResult {
        private String fileName;
        private String expectedText;
        private String recognizedText;
        private boolean isSuccess;
        private long processingTime;
        private String errorMessage;

        public TestResult(String fileName) {
            this.fileName = fileName;
        }

        // Getters and setters
        public String getFileName() {
            return fileName;
        }

        public String getExpectedText() {
            return expectedText;
        }

        public void setExpectedText(String expectedText) {
            this.expectedText = expectedText;
        }

        public String getRecognizedText() {
            return recognizedText;
        }

        public void setRecognizedText(String recognizedText) {
            this.recognizedText = recognizedText;
        }

        public boolean isSuccess() {
            return isSuccess;
        }

        public void setSuccess(boolean success) {
            isSuccess = success;
        }

        public long getProcessingTime() {
            return processingTime;
        }

        public void setProcessingTime(long processingTime) {
            this.processingTime = processingTime;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }

    // 准确率统计
    public static class AccuracyStats {
        private int totalFiles = 0;
        private int successfulRecognitions = 0;
        private int failedRecognitions = 0;
        private long totalProcessingTime = 0;
        private double averageAccuracy = 0.0;
        private List<TestResult> results = new ArrayList<>();

        public void addResult(TestResult result) {
            results.add(result);
            totalFiles++;
            totalProcessingTime += result.getProcessingTime();

            if (result.isSuccess()) {
                successfulRecognitions++;
                // 计算文本相似度作为准确率指标
                double similarity = calculateTextSimilarity(result.getExpectedText(), result.getRecognizedText());
                averageAccuracy = (averageAccuracy * (successfulRecognitions - 1) + similarity) / successfulRecognitions;
            } else {
                failedRecognitions++;
            }
        }

        public double getSuccessRate() {
            return totalFiles > 0 ? (double) successfulRecognitions / totalFiles * 100 : 0;
        }

        public double getAverageProcessingTime() {
            return totalFiles > 0 ? (double) totalProcessingTime / totalFiles : 0;
        }

        // Getters
        public int getTotalFiles() {
            return totalFiles;
        }

        public int getSuccessfulRecognitions() {
            return successfulRecognitions;
        }

        public int getFailedRecognitions() {
            return failedRecognitions;
        }

        public double getAverageAccuracy() {
            return averageAccuracy;
        }

        public List<TestResult> getResults() {
            return results;
        }
    }

    private final ExecutorService executorService;

    public SttAccuracyTest() {
        // 直接实例化SttServiceFactory
        this.sttServiceFactory = new SttServiceFactory();

        // 手动调用初始化方法
        this.sttServiceFactory.initializeDefaultSttService();

        this.executorService = Executors.newFixedThreadPool(10); // 并发处理
    }

    /**
     * 运行STT准确率测试
     *
     * @param audioDirectory    音频文件目录
     * @param expectedTextsFile 期望文本文件路径（可选，如果为null则从文件名推断）
     * @param sttConfigs        STT服务配置列表
     * @return 测试结果统计
     */
    public Map<String, AccuracyStats> runAccuracyTest(String audioDirectory,
                                                      String expectedTextsFile,
                                                      List<SysConfig> sttConfigs) {
        logger.info("开始STT准确率测试，音频目录: {}", audioDirectory);

        Map<String, AccuracyStats> results = new HashMap<>();

        try {
            // 获取音频文件列表
            List<Path> audioFiles = getAudioFiles(audioDirectory).subList(0, 3);
            logger.info("找到 {} 个音频文件", audioFiles.size());

            // 加载期望文本
            Map<String, String> expectedTexts = loadExpectedTexts(expectedTextsFile, audioFiles);

            // 对每个STT配置进行测试
            for (SysConfig config : sttConfigs) {
                String providerName = config.getProvider();
                logger.info("测试STT服务: {}", providerName);

                AccuracyStats stats = testSttService(config, audioFiles, expectedTexts);
                results.put(providerName, stats);

                // 输出当前服务的测试结果
                printServiceResults(providerName, stats);
            }

        } catch (Exception e) {
            logger.error("STT准确率测试失败", e);
        } finally {
            executorService.shutdown();
        }

        return results;
    }

    /**
     * 获取指定目录下的所有音频文件
     */
    private List<Path> getAudioFiles(String directory) throws IOException {
        Path dirPath = Paths.get(directory);
        if (!Files.exists(dirPath) || !Files.isDirectory(dirPath)) {
            throw new IllegalArgumentException("音频目录不存在或不是目录: " + directory);
        }

        try (Stream<Path> paths = Files.walk(dirPath)) {
            return paths
                    .filter(Files::isRegularFile)
                    .filter(path -> {
                        String fileName = path.getFileName().toString().toLowerCase();
                        return SUPPORTED_FORMATS.stream().anyMatch(fileName::endsWith);
                    })
                    .sorted()
                    .toList();
        }
    }

    /**
     * 加载期望文本
     */
    private Map<String, String> loadExpectedTexts(String expectedTextsFile, List<Path> audioFiles) {
        Map<String, String> expectedTexts = new HashMap<>();

        if (expectedTextsFile != null && Files.exists(Paths.get(expectedTextsFile))) {
            // 从文件加载期望文本
            try {
                List<String> lines = Files.readAllLines(Paths.get(expectedTextsFile));
                for (String line : lines) {
                    if (line.trim().isEmpty() || line.startsWith("#")) continue;

                    String[] parts = line.split("\t", 2);
                    if (parts.length == 2) {
                        expectedTexts.put(parts[0].trim(), parts[1].trim());
                    }
                }
                logger.info("从文件加载了 {} 条期望文本", expectedTexts.size());
            } catch (IOException e) {
                logger.warn("无法读取期望文本文件: {}", expectedTextsFile, e);
            }
        }

        // 如果没有期望文本文件，尝试从文件名推断
        if (expectedTexts.isEmpty()) {
            for (Path audioFile : audioFiles) {
                String fileName = audioFile.getFileName().toString();
                String baseName = fileName.substring(0, fileName.lastIndexOf('.'));

                // 尝试从文件名中提取文本（假设文件名包含期望的文本）
                String expectedText = extractTextFromFileName(baseName);
                if (expectedText != null && !expectedText.isEmpty()) {
                    expectedTexts.put(fileName, expectedText);
                }
            }
            logger.info("从文件名推断了 {} 条期望文本", expectedTexts.size());
        }

        return expectedTexts;
    }

    /**
     * 从文件名提取期望文本（可根据实际命名规则调整）
     */
    private String extractTextFromFileName(String baseName) {
        // 示例：如果文件名格式为 "001_你好世界_speaker1"，提取中间部分
        if (baseName.contains("_")) {
            String[] parts = baseName.split("_");
            if (parts.length >= 2) {
                return parts[1]; // 返回第二部分作为期望文本
            }
        }

        // 如果文件名就是期望的文本，直接返回
        return baseName;
    }

    /**
     * 测试单个STT服务
     */
    private AccuracyStats testSttService(SysConfig config, List<Path> audioFiles, Map<String, String> expectedTexts) {
        AccuracyStats stats = new AccuracyStats();

        try {
            // 创建单个STT服务实例，避免重复创建
            SttService sttService = sttServiceFactory.getSttService(config);
            logger.info("使用STT服务: {}", sttService.getProviderName());

            // 检查是否支持流式识别
            boolean supportsStreaming = sttService.supportsStreaming();
            logger.info("STT服务 {} 支持流式识别: {}", sttService.getProviderName(), supportsStreaming);

            // if (supportsStreaming) {
            //     // 流式识别服务使用顺序处理，避免并发连接冲突
            //     logger.info("使用顺序处理模式（流式识别）");
            //     for (Path audioFile : audioFiles) {
            //         TestResult result = processAudioFile(sttService, audioFile, expectedTexts);
            //         stats.addResult(result);
            //     }
            // } else {
                // 批量识别服务可以使用并发处理
                logger.info("使用并发处理模式（批量识别）");
                List<CompletableFuture<TestResult>> futures = audioFiles.stream()
                        .map(audioFile -> CompletableFuture.supplyAsync(() -> processAudioFile(sttService, audioFile, expectedTexts), executorService))
                        .toList();

                // 等待所有任务完成并收集结果
                for (CompletableFuture<TestResult> future : futures) {
                    TestResult result = future.get();
                    stats.addResult(result);
                }
            // }

        } catch (Exception e) {
            logger.error("测试STT服务失败: {}", config.getProvider(), e);
        }

        return stats;
    }

    /**
     * 处理单个音频文件
     */
    private TestResult processAudioFile(SttService sttService, Path audioFile, Map<String, String> expectedTexts) {
        String fileName = audioFile.getFileName().toString();
        TestResult result = new TestResult(fileName);

        try {
            long startTime = System.currentTimeMillis();

            // 读取音频数据
            byte[] audioData = AudioUtils.readAsPcm(audioFile.toString());

            String recognizedText;

            // 优先使用流式识别，如果不支持则使用批量识别
            if (sttService.supportsStreaming()) {
                logger.debug("使用流式识别处理文件: {}", fileName);
                recognizedText = processWithStreamRecognition(sttService, audioData);
            } else {
                logger.debug("使用批量识别处理文件: {}", fileName);
                recognizedText = sttService.recognition(audioData);
            }

            long endTime = System.currentTimeMillis();
            result.setProcessingTime(endTime - startTime);

            // 设置结果
            result.setRecognizedText(recognizedText != null ? recognizedText.trim() : "");
            result.setExpectedText(expectedTexts.getOrDefault(fileName, ""));
            result.setSuccess(recognizedText != null && !recognizedText.trim().isEmpty());

            logger.debug("处理文件: {} -> 识别结果: {}", fileName, recognizedText);

        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            logger.error("处理音频文件失败: {}", fileName, e);
        }

        return result;
    }

    /**
     * 使用流式识别处理音频数据
     */
    private String processWithStreamRecognition(SttService sttService, byte[] audioData) {
        try {
            logger.debug("开始流式识别，音频数据长度: {} bytes", audioData.length);

            // 创建音频数据流
            Sinks.Many<byte[]> audioSink = Sinks.many().multicast().onBackpressureBuffer();

            // 在新线程中启动识别
            var recognitionFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    logger.debug("启动流式识别服务");
                    return sttService.streamRecognition(audioSink);
                } catch (Exception e) {
                    logger.error("流式识别失败", e);
                    return null;
                }
            });

            // 分块发送音频数据，模拟实时流
            int chunkSize = 1600; // 100ms的音频数据 (16kHz * 2 bytes * 0.1s)
            int offset = 0;
            int chunkCount = 0;

            logger.debug("开始发送音频数据，块大小: {} bytes", chunkSize);

            while (offset < audioData.length) {
                int currentChunkSize = Math.min(chunkSize, audioData.length - offset);
                byte[] chunk = new byte[currentChunkSize];
                System.arraycopy(audioData, offset, chunk, 0, currentChunkSize);

                // 发送音频块
                Sinks.EmitResult emitResult = audioSink.tryEmitNext(chunk);
                if (emitResult.isFailure()) {
                    logger.warn("发送音频块失败: {}, 块序号: {}", emitResult, chunkCount);
                } else {
                    logger.debug("发送音频块成功，块序号: {}, 大小: {} bytes", chunkCount, currentChunkSize);
                }

                offset += currentChunkSize;
                chunkCount++;
            }

            logger.debug("音频数据发送完成，总共发送 {} 个块", chunkCount);

            // 完成音频流
            Sinks.EmitResult completeResult = audioSink.tryEmitComplete();
            if (completeResult.isFailure()) {
                logger.warn("完成音频流失败: {}", completeResult);
            } else {
                logger.debug("音频流完成信号发送成功");
            }

            // 等待识别结果，设置较长的超时时间
            logger.debug("等待识别结果...");
            String result = recognitionFuture.get(15, java.util.concurrent.TimeUnit.SECONDS);
            logger.debug("流式识别完成，结果: {}", result);

            return result;

        } catch (java.util.concurrent.TimeoutException e) {
            logger.error("流式识别超时", e);
            return null;
        } catch (InterruptedException e) {
            logger.error("流式识别被中断", e);
            Thread.currentThread().interrupt();
            return null;
        } catch (Exception e) {
            logger.error("流式识别处理失败", e);
            return null;
        }
    }

    /**
     * 计算文本相似度（简单的字符匹配算法）
     */
    private static double calculateTextSimilarity(String expected, String recognized) {
        if (expected == null || recognized == null) return 0.0;
        if (expected.isEmpty() && recognized.isEmpty()) return 1.0;
        if (expected.isEmpty() || recognized.isEmpty()) return 0.0;

        // 移除空格和标点符号，只比较文字内容
        String exp = expected.replaceAll("[\\s\\p{Punct}]", "");
        String rec = recognized.replaceAll("[\\s\\p{Punct}]", "");

        if (exp.equals(rec)) return 1.0;

        // 计算编辑距离
        int distance = levenshteinDistance(exp, rec);
        int maxLength = Math.max(exp.length(), rec.length());

        return maxLength > 0 ? 1.0 - (double) distance / maxLength : 0.0;
    }

    /**
     * 计算编辑距离
     */
    private static int levenshteinDistance(String s1, String s2) {
        int[][] dp = new int[s1.length() + 1][s2.length() + 1];

        for (int i = 0; i <= s1.length(); i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= s2.length(); j++) {
            dp[0][j] = j;
        }

        for (int i = 1; i <= s1.length(); i++) {
            for (int j = 1; j <= s2.length(); j++) {
                if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]) + 1;
                }
            }
        }

        return dp[s1.length()][s2.length()];
    }

    /**
     * 打印单个服务的测试结果
     */
    private void printServiceResults(String providerName, AccuracyStats stats) {
        logger.info("========== {} STT服务测试结果 ==========", providerName);
        logger.info("总文件数: {}", stats.getTotalFiles());
        logger.info("成功识别: {}", stats.getSuccessfulRecognitions());
        logger.info("识别失败: {}", stats.getFailedRecognitions());
        logger.info("成功率: {}%", stats.getSuccessRate());
        logger.info("平均准确率: {}%", stats.getAverageAccuracy() * 100);
        logger.info("平均处理时间: {}ms", stats.getAverageProcessingTime());
        logger.info("==========================================");
    }
}

package com.xiaozhi.dialogue.stt;

import com.xiaozhi.entity.SysConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * STT流式识别调试测试类
 * 专门用于调试和验证流式识别的问题
 */
public class SttStreamingDebugTest {
    
    private static final Logger logger = LoggerFactory.getLogger(SttStreamingDebugTest.class);

    public static void main(String[] args) {
        logger.info("开始STT流式识别调试测试");

        // 测试阿里云流式识别
        testAliyunStreamingRecognition();
    }
    
    /**
     * 测试阿里云流式识别
     */
    private static void testAliyunStreamingRecognition() {
        SttAccuracyTest test = new SttAccuracyTest();
        logger.info("========== 阿里云流式识别调试测试 ==========");

        try {
            // 创建阿里云配置
            List<SysConfig> configs = new ArrayList<>();
            // var aliyunConfig = new SysConfig()
            //     .setProvider("aliyun")
            //     .setName("paraformer-realtime-v2")
            //     .setType("stt")
            //     .setAppId("sk-98d57d03e5f14b8eb99d1867307da6e5")
            //     .setApiKey("sk-98d57d03e5f14b8eb99d1867307da6e5")
            //     .setApiSecret("sk-98d57d03e5f14b8eb99d1867307da6e5");
            // aliyunConfig.setId(4);
            // configs.add(aliyunConfig);

            // var xfyunConfig = new SysConfig()
            //     .setProvider("xfyun")
            //     .setName("paraformer-realtime-v2")
            //     .setType("stt")
            //     .setAppId("50b73263")
            //     .setApiKey("852db8f26941c056c178f06003e34729")
            //     .setApiSecret("NmViZDI0OTM5MmMxMGFiMDQ1YzllMjBl");
            // xfyunConfig.setId(6);
            // configs.add(xfyunConfig);

            var doubaoConfig = new SysConfig()
                .setProvider("doubao")
                .setName("doubao")
                .setType("stt")
                .setAppId("**********")
                .setApiKey("IK8dbBwaIa6wfQjssR5Q164ohUOnl3p7")
                .setApiUrl("wss://openspeech.bytedance.com/api/v3/sauc/bigmodel");
            doubaoConfig.setId(9);
            configs.add(doubaoConfig);
            
            // 设置音频目录
            String audioDirectory = "/Users/<USER>/Downloads/uwav";
            
            logger.info("音频目录: {}", audioDirectory);
            logger.info("开始测试...");
            
            // 运行测试
            Map<String, SttAccuracyTest.AccuracyStats> results = test.runAccuracyTest(
                audioDirectory, null, configs);
            
            // 输出详细结果
            printDetailedResults(results);
            
        } catch (Exception e) {
            logger.error("阿里云流式识别测试失败", e);
        }
    }
    
    /**
     * 打印详细的测试结果
     */
    private static void printDetailedResults(Map<String, SttAccuracyTest.AccuracyStats> results) {
        logger.info("========== 详细测试结果 ==========");
        
        for (Map.Entry<String, SttAccuracyTest.AccuracyStats> entry : results.entrySet()) {
            String provider = entry.getKey();
            SttAccuracyTest.AccuracyStats stats = entry.getValue();
            
            logger.info("STT服务: {}", provider);
            logger.info("  总文件数: {}", stats.getTotalFiles());
            logger.info("  成功识别: {}", stats.getSuccessfulRecognitions());
            logger.info("  识别失败: {}", stats.getFailedRecognitions());
            logger.info("  成功率: {}%", stats.getSuccessRate());
            logger.info("  平均准确率: {}%", stats.getAverageAccuracy() * 100);
            logger.info("  平均处理时间: {}ms", stats.getAverageProcessingTime());
            
            // 输出每个文件的详细结果
            logger.info("  详细结果:");
            for (SttAccuracyTest.TestResult result : stats.getResults()) {
                logger.info("    文件: {}", result.getFileName());
                logger.info("      期望: {}", result.getExpectedText());
                logger.info("      识别: {}", result.getRecognizedText());
                logger.info("      成功: {}", result.isSuccess());
                logger.info("      耗时: {}ms", result.getProcessingTime());
                if (!result.isSuccess() && result.getErrorMessage() != null) {
                    logger.info("      错误: {}", result.getErrorMessage());
                }
                logger.info("    ---");
            }
            logger.info("");
        }
        
        // 分析问题
        analyzeResults(results);
    }
    
    /**
     * 分析测试结果，找出问题
     */
    private static void analyzeResults(Map<String, SttAccuracyTest.AccuracyStats> results) {
        logger.info("========== 问题分析 ==========");
        
        for (Map.Entry<String, SttAccuracyTest.AccuracyStats> entry : results.entrySet()) {
            String provider = entry.getKey();
            SttAccuracyTest.AccuracyStats stats = entry.getValue();
            
            logger.info("分析服务: {}", provider);
            
            // 检查超时问题
            int timeoutCount = 0;
            int emptyResultCount = 0;
            int errorCount = 0;
            
            for (SttAccuracyTest.TestResult result : stats.getResults()) {
                if (!result.isSuccess()) {
                    if (result.getErrorMessage() != null) {
                        if (result.getErrorMessage().contains("timeout") || 
                            result.getErrorMessage().contains("超时")) {
                            timeoutCount++;
                        } else {
                            errorCount++;
                        }
                    } else if (result.getRecognizedText() == null || 
                               result.getRecognizedText().trim().isEmpty()) {
                        emptyResultCount++;
                    }
                }
            }
            
            logger.info("  超时失败: {} 个", timeoutCount);
            logger.info("  空结果: {} 个", emptyResultCount);
            logger.info("  其他错误: {} 个", errorCount);
            
            // 给出建议
            if (timeoutCount > 0) {
                logger.info("  建议: 增加超时时间或检查网络连接");
            }
            if (emptyResultCount > 0) {
                logger.info("  建议: 检查音频数据是否正确发送到STT服务");
            }
            if (errorCount > 0) {
                logger.info("  建议: 检查STT服务配置和API密钥");
            }
            
            // 检查处理时间分布
            double avgTime = stats.getAverageProcessingTime();
            logger.info("  平均处理时间: {:.2f}ms", avgTime);
            
            if (avgTime > 30000) {
                logger.info("  警告: 处理时间过长，可能存在超时问题");
            } else if (avgTime < 1000) {
                logger.info("  警告: 处理时间过短，可能未正确处理音频数据");
            }
            
            logger.info("");
        }
        
        // 总体建议
        logger.info("========== 修复建议 ==========");
        logger.info("1. 确保音频文件格式正确（PCM, 16kHz, 16-bit, mono）");
        logger.info("2. 检查网络连接稳定性");
        logger.info("3. 验证API密钥配置正确");
        logger.info("4. 考虑增加流式识别的初始化等待时间");
        logger.info("5. 检查音频数据块发送是否成功");
        logger.info("6. 监控STT服务的连接状态");
    }
    
    /**
     * 单独测试单个音频文件
     */
    public static void testSingleAudioFile(String audioFilePath) {
        logger.info("========== 单文件测试 ==========");
        logger.info("测试文件: {}", audioFilePath);
        
        try {
            // 创建阿里云配置
            SysConfig aliyunConfig = new SysConfig()
                .setProvider("aliyun")
                .setName("paraformer-realtime-v2")
                .setType("stt")
                .setAppId("sk-98d57d03e5f14b8eb99d1867307da6e5")
                .setApiKey("sk-98d57d03e5f14b8eb99d1867307da6e5")
                .setApiSecret("sk-98d57d03e5f14b8eb99d1867307da6e5");
            aliyunConfig.setId(4);
            
            // 创建STT服务
            var sttServiceFactory = new com.xiaozhi.dialogue.stt.factory.SttServiceFactory();
            var sttService = sttServiceFactory.getSttService(aliyunConfig);
            
            logger.info("STT服务: {}", sttService.getProviderName());
            logger.info("支持流式识别: {}", sttService.supportsStreaming());
            
            // 读取音频文件
            byte[] audioData = com.xiaozhi.utils.AudioUtils.readAsPcm(audioFilePath);
            logger.info("音频数据长度: {} bytes", audioData.length);
            
            // 测试流式识别
            if (sttService.supportsStreaming()) {
                logger.info("开始流式识别测试...");
                long startTime = System.currentTimeMillis();
                
                // 直接调用流式识别方法
                reactor.core.publisher.Sinks.Many<byte[]> audioSink =
                    reactor.core.publisher.Sinks.many().multicast().onBackpressureBuffer();

                // 在新线程中启动识别
                java.util.concurrent.CompletableFuture<String> recognitionFuture =
                    java.util.concurrent.CompletableFuture.supplyAsync(() -> {
                        try {
                            return sttService.streamRecognition(audioSink);
                        } catch (Exception e) {
                            logger.error("流式识别失败", e);
                            return null;
                        }
                    });

                // 给识别服务一些时间来初始化连接
                Thread.sleep(500);

                // 分块发送音频数据
                int chunkSize = 1600; // 100ms的音频数据
                int offset = 0;
                int chunkCount = 0;

                while (offset < audioData.length) {
                    int currentChunkSize = Math.min(chunkSize, audioData.length - offset);
                    byte[] chunk = new byte[currentChunkSize];
                    System.arraycopy(audioData, offset, chunk, 0, currentChunkSize);

                    audioSink.tryEmitNext(chunk);
                    offset += currentChunkSize;
                    chunkCount++;

                    Thread.sleep(100); // 模拟实时流
                }

                logger.info("发送了 {} 个音频块", chunkCount);
                audioSink.tryEmitComplete();

                // 等待识别结果
                String result = recognitionFuture.get(60, java.util.concurrent.TimeUnit.SECONDS);
                
                long endTime = System.currentTimeMillis();
                
                logger.info("识别结果: {}", result);
                logger.info("处理时间: {}ms", endTime - startTime);
                
                if (result == null || result.trim().isEmpty()) {
                    logger.warn("识别结果为空，可能存在问题");
                } else {
                    logger.info("识别成功！");
                }
            } else {
                logger.warn("服务不支持流式识别");
            }
            
        } catch (Exception e) {
            logger.error("单文件测试失败", e);
        }
    }
}

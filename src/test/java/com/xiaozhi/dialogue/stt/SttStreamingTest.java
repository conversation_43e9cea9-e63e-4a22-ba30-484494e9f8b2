package com.xiaozhi.dialogue.stt;

import com.xiaozhi.dialogue.stt.factory.SttServiceFactory;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.utils.AudioUtils;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import reactor.core.publisher.Sinks;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * STT流式识别专用测试类
 * 专门测试支持流式识别的STT服务
 * 传统单元测试方式，不依赖Spring框架
 */
public class SttStreamingTest {

    private static final Logger logger = LoggerFactory.getLogger(SttStreamingTest.class);

    private final SttServiceFactory sttServiceFactory;
    
    // 支持的音频文件格式
    private static final Set<String> SUPPORTED_FORMATS = Set.of(".wav", ".mp3", ".pcm", ".opus");
    
    // 流式测试结果
    @Data
    public static class StreamingTestResult {
        private String fileName;
        private String providerName;
        private String expectedText;
        private String recognizedText;
        private boolean isSuccess;
        private long processingTime;
        private String errorMessage;
        private boolean usedStreaming;
        private int audioChunks;
        private double averageChunkTime;
        
        public StreamingTestResult(String fileName, String providerName) {
            this.fileName = fileName;
            this.providerName = providerName;
        }

    }
    
    // 流式测试统计
    public static class StreamingStats {
        private int totalFiles = 0;
        private int streamingSupported = 0;
        private int streamingSuccess = 0;
        private int streamingFailed = 0;
        private long totalProcessingTime = 0;
        private double averageAccuracy = 0.0;
        private List<StreamingTestResult> results = new ArrayList<>();
        
        public void addResult(StreamingTestResult result) {
            results.add(result);
            totalFiles++;
            totalProcessingTime += result.getProcessingTime();
            
            if (result.isUsedStreaming()) {
                streamingSupported++;
                if (result.isSuccess()) {
                    streamingSuccess++;
                    // 计算文本相似度
                    double similarity = calculateTextSimilarity(result.getExpectedText(), result.getRecognizedText());
                    averageAccuracy = (averageAccuracy * (streamingSuccess - 1) + similarity) / streamingSuccess;
                } else {
                    streamingFailed++;
                }
            }
        }
        
        public double getStreamingSupportRate() {
            return totalFiles > 0 ? (double) streamingSupported / totalFiles * 100 : 0;
        }
        
        public double getStreamingSuccessRate() {
            return streamingSupported > 0 ? (double) streamingSuccess / streamingSupported * 100 : 0;
        }
        
        public double getAverageProcessingTime() {
            return totalFiles > 0 ? (double) totalProcessingTime / totalFiles : 0;
        }
        
        // Getters
        public int getTotalFiles() { return totalFiles; }
        public int getStreamingSupported() { return streamingSupported; }
        public int getStreamingSuccess() { return streamingSuccess; }
        public int getStreamingFailed() { return streamingFailed; }
        public double getAverageAccuracy() { return averageAccuracy; }
        public List<StreamingTestResult> getResults() { return results; }
    }
    
    private final ExecutorService executorService;

    public SttStreamingTest() {
        // 直接实例化SttServiceFactory
        this.sttServiceFactory = new SttServiceFactory();

        // 手动调用初始化方法
        this.sttServiceFactory.initializeDefaultSttService();

        this.executorService = Executors.newFixedThreadPool(2); // 减少并发数，避免流式连接冲突
    }
    
    /**
     * 运行流式识别测试
     */
    public Map<String, StreamingStats> runStreamingTest(String audioDirectory, 
                                                       String expectedTextsFile, 
                                                       List<SysConfig> sttConfigs) {
        logger.info("开始STT流式识别测试，音频目录: {}", audioDirectory);
        
        Map<String, StreamingStats> results = new HashMap<>();
        
        try {
            // 获取音频文件列表
            List<Path> audioFiles = getAudioFiles(audioDirectory);
            logger.info("找到 {} 个音频文件", audioFiles.size());
            
            // 加载期望文本
            Map<String, String> expectedTexts = loadExpectedTexts(expectedTextsFile, audioFiles);
            
            // 对每个STT配置进行测试
            for (SysConfig config : sttConfigs) {
                String providerName = config.getProvider();
                logger.info("测试STT服务流式识别: {}", providerName);
                
                StreamingStats stats = testStreamingSttService(config, audioFiles, expectedTexts);
                results.put(providerName, stats);
                
                // 输出当前服务的测试结果
                printStreamingResults(providerName, stats);
            }
            
        } catch (Exception e) {
            logger.error("STT流式识别测试失败", e);
        } finally {
            executorService.shutdown();
        }
        
        return results;
    }
    
    /**
     * 测试单个STT服务的流式识别
     */
    private StreamingStats testStreamingSttService(SysConfig config, List<Path> audioFiles, Map<String, String> expectedTexts) {
        StreamingStats stats = new StreamingStats();
        
        try {
            SttService sttService = sttServiceFactory.getSttService(config);
            logger.info("使用STT服务: {}，支持流式识别: {}", sttService.getProviderName(), sttService.supportsStreaming());
            
            // 顺序处理音频文件（避免流式连接冲突）
            for (Path audioFile : audioFiles) {
                StreamingTestResult result = processStreamingAudioFile(sttService, audioFile, expectedTexts);
                stats.addResult(result);
                
                // 在流式识别之间添加短暂延迟，避免连接冲突
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
        } catch (Exception e) {
            logger.error("测试STT流式服务失败: {}", config.getProvider(), e);
        }
        
        return stats;
    }
    
    /**
     * 处理单个音频文件的流式识别
     */
    private StreamingTestResult processStreamingAudioFile(SttService sttService, Path audioFile, Map<String, String> expectedTexts) {
        String fileName = audioFile.getFileName().toString();
        StreamingTestResult result = new StreamingTestResult(fileName, sttService.getProviderName());
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 读取音频数据
            byte[] audioData = AudioUtils.readAsPcm(audioFile.toString());
            
            String recognizedText;
            boolean usedStreaming = false;
            int audioChunks = 0;
            
            if (sttService.supportsStreaming()) {
                logger.debug("使用流式识别处理文件: {}", fileName);
                recognizedText = processWithAdvancedStreamRecognition(sttService, audioData);
                usedStreaming = true;
                audioChunks = calculateAudioChunks(audioData.length);
            } else {
                logger.debug("服务不支持流式识别，使用批量识别: {}", fileName);
                recognizedText = sttService.recognition(audioData);
            }
            
            long endTime = System.currentTimeMillis();
            long processingTime = endTime - startTime;
            
            // 设置结果
            result.setRecognizedText(recognizedText != null ? recognizedText.trim() : "");
            result.setExpectedText(expectedTexts.getOrDefault(fileName, ""));
            result.setSuccess(recognizedText != null && !recognizedText.trim().isEmpty());
            result.setProcessingTime(processingTime);
            result.setUsedStreaming(usedStreaming);
            result.setAudioChunks(audioChunks);
            result.setAverageChunkTime(audioChunks > 0 ? (double) processingTime / audioChunks : 0);
            
            logger.debug("处理文件: {} -> 识别结果: {} (流式: {})", fileName, recognizedText, usedStreaming);
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            logger.error("处理音频文件失败: {}", fileName, e);
        }
        
        return result;
    }
    
    /**
     * 高级流式识别处理，模拟更真实的流式场景
     */
    private String processWithAdvancedStreamRecognition(SttService sttService, byte[] audioData) {
        try {
            // 创建音频数据流
            Sinks.Many<byte[]> audioSink = Sinks.many().multicast().onBackpressureBuffer();
            
            // 在新线程中启动识别
            CompletableFuture<String> recognitionFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return sttService.streamRecognition(audioSink);
                } catch (Exception e) {
                    logger.error("流式识别失败", e);
                    return null;
                }
            });
            
            // 模拟实时音频流，分块发送
            int chunkSize = 1600; // 100ms的音频数据 (16kHz * 2 bytes * 0.1s)
            int offset = 0;
            
            while (offset < audioData.length) {
                int currentChunkSize = Math.min(chunkSize, audioData.length - offset);
                byte[] chunk = new byte[currentChunkSize];
                System.arraycopy(audioData, offset, chunk, 0, currentChunkSize);
                
                // 发送音频块
                audioSink.tryEmitNext(chunk);
                offset += currentChunkSize;
                
                // 模拟实时流的延迟（100ms）
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
            // 完成音频流
            audioSink.tryEmitComplete();
            
            // 等待识别结果，设置较长的超时时间
            return recognitionFuture.get(60, TimeUnit.SECONDS);
            
        } catch (Exception e) {
            logger.error("高级流式识别处理失败", e);
            return null;
        }
    }
    
    /**
     * 计算音频块数量
     */
    private int calculateAudioChunks(int audioDataLength) {
        int chunkSize = 1600; // 100ms chunks
        return (audioDataLength + chunkSize - 1) / chunkSize;
    }
    
    /**
     * 获取音频文件列表
     */
    private List<Path> getAudioFiles(String directory) throws IOException {
        Path dirPath = Paths.get(directory);
        if (!Files.exists(dirPath) || !Files.isDirectory(dirPath)) {
            throw new IllegalArgumentException("音频目录不存在或不是目录: " + directory);
        }
        
        try (Stream<Path> paths = Files.walk(dirPath)) {
            return paths
                .filter(Files::isRegularFile)
                .filter(path -> {
                    String fileName = path.getFileName().toString().toLowerCase();
                    return SUPPORTED_FORMATS.stream().anyMatch(fileName::endsWith);
                })
                .sorted()
                .toList();
        }
    }
    
    /**
     * 加载期望文本（简化版本）
     */
    private Map<String, String> loadExpectedTexts(String expectedTextsFile, List<Path> audioFiles) {
        Map<String, String> expectedTexts = new HashMap<>();
        
        if (expectedTextsFile != null && Files.exists(Paths.get(expectedTextsFile))) {
            try {
                List<String> lines = Files.readAllLines(Paths.get(expectedTextsFile));
                for (String line : lines) {
                    if (line.trim().isEmpty() || line.startsWith("#")) continue;
                    
                    String[] parts = line.split("\t", 2);
                    if (parts.length == 2) {
                        expectedTexts.put(parts[0].trim(), parts[1].trim());
                    }
                }
                logger.info("从文件加载了 {} 条期望文本", expectedTexts.size());
            } catch (IOException e) {
                logger.warn("无法读取期望文本文件: {}", expectedTextsFile, e);
            }
        }
        
        // 如果没有期望文本，从文件名推断
        if (expectedTexts.isEmpty()) {
            for (Path audioFile : audioFiles) {
                String fileName = audioFile.getFileName().toString();
                String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
                expectedTexts.put(fileName, baseName);
            }
        }
        
        return expectedTexts;
    }
    
    /**
     * 计算文本相似度
     */
    private static double calculateTextSimilarity(String expected, String recognized) {
        if (expected == null || recognized == null) return 0.0;
        if (expected.isEmpty() && recognized.isEmpty()) return 1.0;
        if (expected.isEmpty() || recognized.isEmpty()) return 0.0;
        
        // 移除空格和标点符号
        String exp = expected.replaceAll("[\\s\\p{Punct}]", "");
        String rec = recognized.replaceAll("[\\s\\p{Punct}]", "");
        
        if (exp.equals(rec)) return 1.0;
        
        // 简单的编辑距离计算
        int distance = levenshteinDistance(exp, rec);
        int maxLength = Math.max(exp.length(), rec.length());
        
        return maxLength > 0 ? 1.0 - (double) distance / maxLength : 0.0;
    }
    
    /**
     * 编辑距离计算
     */
    private static int levenshteinDistance(String s1, String s2) {
        int[][] dp = new int[s1.length() + 1][s2.length() + 1];
        
        for (int i = 0; i <= s1.length(); i++) dp[i][0] = i;
        for (int j = 0; j <= s2.length(); j++) dp[0][j] = j;
        
        for (int i = 1; i <= s1.length(); i++) {
            for (int j = 1; j <= s2.length(); j++) {
                if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]) + 1;
                }
            }
        }
        
        return dp[s1.length()][s2.length()];
    }
    
    /**
     * 打印流式识别测试结果
     */
    private void printStreamingResults(String providerName, StreamingStats stats) {
        logger.info("========== {} 流式识别测试结果 ==========", providerName);
        logger.info("总文件数: {}", stats.getTotalFiles());
        logger.info("支持流式识别: {}", stats.getStreamingSupported());
        logger.info("流式识别成功: {}", stats.getStreamingSuccess());
        logger.info("流式识别失败: {}", stats.getStreamingFailed());
        logger.info("流式支持率: {:.2f}%", stats.getStreamingSupportRate());
        logger.info("流式成功率: {:.2f}%", stats.getStreamingSuccessRate());
        logger.info("平均准确率: {:.2f}%", stats.getAverageAccuracy() * 100);
        logger.info("平均处理时间: {:.2f}ms", stats.getAverageProcessingTime());
        logger.info("==========================================");
    }
}

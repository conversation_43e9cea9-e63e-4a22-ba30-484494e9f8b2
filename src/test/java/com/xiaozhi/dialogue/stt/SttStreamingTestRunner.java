package com.xiaozhi.dialogue.stt;

import com.xiaozhi.entity.SysConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * STT流式识别测试运行器
 * 专门用于测试支持流式识别的STT服务
 */
public class SttStreamingTestRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(SttStreamingTestRunner.class);

    public static void main(String[] args) {
        // 解析命令行参数
        String audioDirectory = getArgument(args, "--audio-dir", "./audio");
        String expectedTextsFile = getArgument(args, "--expected-texts", null);
        String outputReport = getArgument(args, "--output", "./stt_streaming_test_report.txt");
        boolean enableVosk = hasArgument(args, "--enable-vosk");
        boolean enableAllProviders = hasArgument(args, "--enable-all");
        
        logger.info("STT流式识别测试开始");
        logger.info("音频目录: {}", audioDirectory);
        logger.info("期望文本文件: {}", expectedTextsFile != null ? expectedTextsFile : "从文件名推断");
        logger.info("输出报告: {}", outputReport);
        
        // 直接创建测试实例
        SttStreamingTest test = new SttStreamingTest();

        // 配置STT服务（只包含支持流式识别的服务）
        List<SysConfig> sttConfigs = createStreamingSttConfigs(enableVosk, enableAllProviders);

        if (sttConfigs.isEmpty()) {
            logger.error("没有配置任何支持流式识别的STT服务");
            return;
        }

        // 运行流式测试
        Map<String, SttStreamingTest.StreamingStats> results = test.runStreamingTest(
            audioDirectory, expectedTextsFile, sttConfigs);
        
        // 生成测试报告
        generateStreamingTestReport(results, outputReport);
        
        logger.info("STT流式识别测试完成，报告已保存到: {}", outputReport);
    }
    
    /**
     * 创建支持流式识别的STT服务配置列表
     */
    private static List<SysConfig> createStreamingSttConfigs(boolean enableVosk, boolean enableAllProviders) {
        List<SysConfig> configs = new ArrayList<>();
        
        // Vosk本地服务（支持流式识别）
        if (enableVosk || enableAllProviders) {
            SysConfig voskConfig = new SysConfig()
                .setProvider("vosk")
                .setName("Vosk本地流式识别")
                .setType("stt");
            voskConfig.setId(-1);
            configs.add(voskConfig);
        }
        
        if (enableAllProviders) {
            // 腾讯云STT（支持流式识别）
            String tencentSecretId = System.getenv("TENCENT_SECRET_ID");
            String tencentSecretKey = System.getenv("TENCENT_SECRET_KEY");
            if (tencentSecretId != null && tencentSecretKey != null) {
                SysConfig tencentConfig = new SysConfig()
                    .setProvider("tencent")
                    .setName("腾讯云流式STT")
                    .setType("stt")
                    .setApiKey(tencentSecretId)
                    .setApiSecret(tencentSecretKey)
                    .setApiUrl("https://asr.tencentcloudapi.com/");
                tencentConfig.setId(1);
                configs.add(tencentConfig);
            }
            
            // 阿里云STT（支持流式识别）
            String aliyunApiKey = System.getenv("ALIYUN_API_KEY");
            if (aliyunApiKey != null) {
                SysConfig aliyunConfig = new SysConfig()
                    .setProvider("aliyun")
                    .setName("阿里云流式STT")
                    .setType("stt")
                    .setApiKey(aliyunApiKey);
                aliyunConfig.setId(2);
                configs.add(aliyunConfig);
            }
            
            // 讯飞STT（支持流式识别）
            String xfyunAppId = System.getenv("XFYUN_APP_ID");
            String xfyunApiKey = System.getenv("XFYUN_API_KEY");
            String xfyunApiSecret = System.getenv("XFYUN_API_SECRET");
            if (xfyunAppId != null && xfyunApiKey != null && xfyunApiSecret != null) {
                SysConfig xfyunConfig = new SysConfig()
                    .setProvider("xfyun")
                    .setName("讯飞流式STT")
                    .setType("stt")
                    .setAppId(xfyunAppId)
                    .setApiKey(xfyunApiKey)
                    .setApiSecret(xfyunApiSecret);
                xfyunConfig.setId(3);
                configs.add(xfyunConfig);
            }
            
            // Azure STT（支持流式识别）
            String azureSubscriptionKey = System.getenv("AZURE_SUBSCRIPTION_KEY");
            String azureRegion = System.getenv("AZURE_REGION");
            if (azureSubscriptionKey != null && azureRegion != null) {
                SysConfig azureConfig = new SysConfig()
                    .setProvider("azure")
                    .setName("Azure流式STT")
                    .setType("stt")
                    .setApiKey(azureSubscriptionKey)
                    .setApiSecret(azureRegion)
                    .setApiUrl("zh-CN");
                azureConfig.setId(4);
                configs.add(azureConfig);
            }
            
            // FunASR（仅支持流式识别）
            String funasrApiUrl = System.getenv("FUNASR_API_URL");
            if (funasrApiUrl != null) {
                SysConfig funasrConfig = new SysConfig()
                    .setProvider("funasr")
                    .setName("FunASR流式识别")
                    .setType("stt")
                    .setApiUrl(funasrApiUrl);
                funasrConfig.setId(5);
                configs.add(funasrConfig);
            }
            
            // 豆包ASR（仅支持流式识别）
            String doubaoAppId = System.getenv("DOUBAO_APP_ID");
            String doubaoApiKey = System.getenv("DOUBAO_API_KEY");
            if (doubaoAppId != null && doubaoApiKey != null) {
                SysConfig doubaoConfig = new SysConfig()
                    .setProvider("doubao")
                    .setName("豆包流式ASR")
                    .setType("stt")
                    .setAppId(doubaoAppId)
                    .setApiKey(doubaoApiKey)
                    .setApiUrl("wss://openspeech.bytedance.com/api/v1/sauc");
                doubaoConfig.setId(6);
                configs.add(doubaoConfig);
            }
            
            // 有道STT（仅支持流式识别）
            String youdaoAppId = System.getenv("YOUDAO_APP_ID");
            String youdaoAppSecret = System.getenv("YOUDAO_APP_SECRET");
            if (youdaoAppId != null && youdaoAppSecret != null) {
                SysConfig youdaoConfig = new SysConfig()
                    .setProvider("youdao")
                    .setName("有道流式STT")
                    .setType("stt")
                    .setAppId(youdaoAppId)
                    .setApiSecret(youdaoAppSecret);
                youdaoConfig.setId(7);
                configs.add(youdaoConfig);
            }
        }
        
        return configs;
    }
    
    /**
     * 生成流式识别测试报告
     */
    private static void generateStreamingTestReport(Map<String, SttStreamingTest.StreamingStats> results, String outputFile) {
        try (FileWriter writer = new FileWriter(outputFile)) {
            writer.write("STT流式识别测试报告\n");
            writer.write("生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n");
            writer.write("=" + "=".repeat(80) + "\n\n");
            
            // 总体统计
            writer.write("总体统计:\n");
            writer.write("-".repeat(40) + "\n");
            int totalProviders = results.size();
            writer.write(String.format("测试的STT服务数量: %d\n", totalProviders));
            
            if (!results.isEmpty()) {
                int totalFiles = results.values().iterator().next().getTotalFiles();
                writer.write(String.format("测试音频文件数量: %d\n", totalFiles));
            }
            writer.write("\n");
            
            // 各服务详细结果
            writer.write("各服务流式识别详细结果:\n");
            writer.write("=" + "=".repeat(80) + "\n");
            
            for (Map.Entry<String, SttStreamingTest.StreamingStats> entry : results.entrySet()) {
                String provider = entry.getKey();
                SttStreamingTest.StreamingStats stats = entry.getValue();
                
                writer.write(String.format("\n【%s】\n", provider));
                writer.write("-".repeat(40) + "\n");
                writer.write(String.format("总文件数: %d\n", stats.getTotalFiles()));
                writer.write(String.format("支持流式识别: %d\n", stats.getStreamingSupported()));
                writer.write(String.format("流式识别成功: %d\n", stats.getStreamingSuccess()));
                writer.write(String.format("流式识别失败: %d\n", stats.getStreamingFailed()));
                writer.write(String.format("流式支持率: %.2f%%\n", stats.getStreamingSupportRate()));
                writer.write(String.format("流式成功率: %.2f%%\n", stats.getStreamingSuccessRate()));
                writer.write(String.format("平均准确率: %.2f%%\n", stats.getAverageAccuracy() * 100));
                writer.write(String.format("平均处理时间: %.2f ms\n", stats.getAverageProcessingTime()));
                
                // 详细结果
                writer.write("\n详细结果:\n");
                for (SttStreamingTest.StreamingTestResult result : stats.getResults()) {
                    writer.write(String.format("  文件: %s\n", result.getFileName()));
                    writer.write(String.format("    期望: %s\n", result.getExpectedText()));
                    writer.write(String.format("    识别: %s\n", result.getRecognizedText()));
                    writer.write(String.format("    状态: %s\n", result.isSuccess() ? "成功" : "失败"));
                    writer.write(String.format("    使用流式: %s\n", result.isUsedStreaming() ? "是" : "否"));
                    writer.write(String.format("    音频块数: %d\n", result.getAudioChunks()));
                    writer.write(String.format("    平均块处理时间: %.2f ms\n", result.getAverageChunkTime()));
                    writer.write(String.format("    总耗时: %d ms\n", result.getProcessingTime()));
                    if (!result.isSuccess() && result.getErrorMessage() != null) {
                        writer.write(String.format("    错误: %s\n", result.getErrorMessage()));
                    }
                    writer.write("\n");
                }
            }
            
            // 流式识别性能对比
            writer.write("\n流式识别性能对比:\n");
            writer.write("=" + "=".repeat(80) + "\n");
            writer.write(String.format("%-15s %-10s %-12s %-15s %-15s %-15s\n", 
                "服务", "支持率", "成功率", "平均准确率", "平均处理时间", "总处理文件"));
            writer.write("-".repeat(90) + "\n");
            
            for (Map.Entry<String, SttStreamingTest.StreamingStats> entry : results.entrySet()) {
                String provider = entry.getKey();
                SttStreamingTest.StreamingStats stats = entry.getValue();
                
                writer.write(String.format("%-15s %-10.2f %-12.2f %-15.2f %-15.2f %-15d\n",
                    provider,
                    stats.getStreamingSupportRate(),
                    stats.getStreamingSuccessRate(),
                    stats.getAverageAccuracy() * 100,
                    stats.getAverageProcessingTime(),
                    stats.getTotalFiles()));
            }
            
            // 流式识别优势分析
            writer.write("\n\n流式识别优势分析:\n");
            writer.write("=" + "=".repeat(80) + "\n");
            writer.write("1. 实时性: 流式识别可以在音频播放过程中实时返回识别结果\n");
            writer.write("2. 内存效率: 不需要缓存完整音频文件，适合长音频处理\n");
            writer.write("3. 用户体验: 用户可以更快看到识别结果，提升交互体验\n");
            writer.write("4. 网络优化: 可以边传输边处理，减少网络延迟影响\n");
            writer.write("\n注意事项:\n");
            writer.write("- 流式识别对网络稳定性要求较高\n");
            writer.write("- 某些服务可能对并发流式连接有限制\n");
            writer.write("- 流式识别的准确率可能略低于批量识别\n");
            
        } catch (IOException e) {
            logger.error("生成流式识别测试报告失败", e);
        }
    }
    
    /**
     * 获取命令行参数值
     */
    private static String getArgument(String[] args, String key, String defaultValue) {
        for (int i = 0; i < args.length - 1; i++) {
            if (args[i].equals(key)) {
                return args[i + 1];
            }
        }
        return defaultValue;
    }
    
    /**
     * 检查是否存在指定的命令行参数
     */
    private static boolean hasArgument(String[] args, String key) {
        for (String arg : args) {
            if (arg.equals(key)) {
                return true;
            }
        }
        return false;
    }
}

package com.xiaozhi.dialogue.stt;

import com.xiaozhi.entity.SysConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * STT测试运行器
 * 配置和运行STT准确率测试，生成详细的测试报告
 */
public class SttTestRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(SttTestRunner.class);

    public static void main(String[] args) {
        // 解析命令行参数
        String audioDirectory = getArgument(args, "--audio-dir", "./audio");
        String expectedTextsFile = getArgument(args, "--expected-texts", null);
        String outputReport = getArgument(args, "--output", "./stt_test_report.txt");
        boolean enableVosk = hasArgument(args, "--enable-vosk");
        boolean enableAllProviders = hasArgument(args, "--enable-all");
        boolean forceStreaming = hasArgument(args, "--force-streaming");
        
        logger.info("STT准确率测试开始");
        logger.info("音频目录: {}", audioDirectory);
        logger.info("期望文本文件: {}", expectedTextsFile != null ? expectedTextsFile : "从文件名推断");
        logger.info("输出报告: {}", outputReport);
        logger.info("强制使用流式识别: {}", forceStreaming);

        // 直接创建测试实例
        SttAccuracyTest test = new SttAccuracyTest();

        // 配置STT服务
        List<SysConfig> sttConfigs = createSttConfigs(enableVosk, enableAllProviders);

        if (sttConfigs.isEmpty()) {
            logger.error("没有配置任何STT服务，请检查配置或使用 --enable-all 参数");
            return;
        }

        // 运行测试
        Map<String, SttAccuracyTest.AccuracyStats> results = test.runAccuracyTest(
            audioDirectory, expectedTextsFile, sttConfigs);
        
        // 生成测试报告
        generateTestReport(results, outputReport);
        
        logger.info("STT准确率测试完成，报告已保存到: {}", outputReport);
    }
    
    /**
     * 创建STT服务配置列表
     */
    private static List<SysConfig> createSttConfigs(boolean enableVosk, boolean enableAllProviders) {
        List<SysConfig> configs = new ArrayList<>();
        
        // Vosk本地服务（默认）
        if (enableVosk || enableAllProviders) {
            SysConfig voskConfig = new SysConfig()
                .setProvider("vosk")
                .setName("Vosk本地识别")
                .setType("stt");
            voskConfig.setId(-1);
            configs.add(voskConfig);
        }
        
        if (enableAllProviders) {
            // 腾讯云STT（需要配置API密钥）
            String tencentSecretId = System.getenv("TENCENT_SECRET_ID");
            String tencentSecretKey = System.getenv("TENCENT_SECRET_KEY");
            if (tencentSecretId != null && tencentSecretKey != null) {
                SysConfig tencentConfig = new SysConfig()
                    .setProvider("tencent")
                    .setName("腾讯云STT")
                    .setType("stt")
                    .setApiKey(tencentSecretId)
                    .setApiSecret(tencentSecretKey)
                    .setApiUrl("https://asr.tencentcloudapi.com/");
                tencentConfig.setId(1);
                configs.add(tencentConfig);
            }
            
            // 阿里云STT（需要配置API密钥）
            String aliyunApiKey = System.getenv("ALIYUN_API_KEY");
            if (aliyunApiKey != null) {
                SysConfig aliyunConfig = new SysConfig()
                    .setProvider("aliyun")
                    .setName("阿里云STT")
                    .setType("stt")
                    .setApiKey(aliyunApiKey);
                aliyunConfig.setId(2);
                configs.add(aliyunConfig);
            }
            
            // 讯飞STT（需要配置API密钥）
            String xfyunAppId = System.getenv("XFYUN_APP_ID");
            String xfyunApiKey = System.getenv("XFYUN_API_KEY");
            String xfyunApiSecret = System.getenv("XFYUN_API_SECRET");
            if (xfyunAppId != null && xfyunApiKey != null && xfyunApiSecret != null) {
                SysConfig xfyunConfig = new SysConfig()
                    .setProvider("xfyun")
                    .setName("讯飞STT")
                    .setType("stt")
                    .setAppId(xfyunAppId)
                    .setApiKey(xfyunApiKey)
                    .setApiSecret(xfyunApiSecret);
                xfyunConfig.setId(3);
                configs.add(xfyunConfig);
            }
            
            // Azure STT（需要配置API密钥）
            String azureSubscriptionKey = System.getenv("AZURE_SUBSCRIPTION_KEY");
            String azureRegion = System.getenv("AZURE_REGION");
            if (azureSubscriptionKey != null && azureRegion != null) {
                SysConfig azureConfig = new SysConfig()
                    .setProvider("azure")
                    .setName("Azure STT")
                    .setType("stt")
                    .setApiKey(azureSubscriptionKey)
                    .setApiSecret(azureRegion)
                    .setApiUrl("zh-CN");
                azureConfig.setId(4);
                configs.add(azureConfig);
            }
            
            // FunASR（需要配置API地址）
            String funasrApiUrl = System.getenv("FUNASR_API_URL");
            if (funasrApiUrl != null) {
                SysConfig funasrConfig = new SysConfig()
                    .setProvider("funasr")
                    .setName("FunASR")
                    .setType("stt")
                    .setApiUrl(funasrApiUrl);
                funasrConfig.setId(5);
                configs.add(funasrConfig);
            }
            
            // 豆包ASR（需要配置API密钥）
            String doubaoAppId = System.getenv("DOUBAO_APP_ID");
            String doubaoApiKey = System.getenv("DOUBAO_API_KEY");
            if (doubaoAppId != null && doubaoApiKey != null) {
                SysConfig doubaoConfig = new SysConfig()
                    .setProvider("doubao")
                    .setName("豆包ASR")
                    .setType("stt")
                    .setAppId(doubaoAppId)
                    .setApiKey(doubaoApiKey)
                    .setApiUrl("wss://openspeech.bytedance.com/api/v1/sauc");
                doubaoConfig.setId(6);
                configs.add(doubaoConfig);
            }
            
            // 有道STT（需要配置API密钥）
            String youdaoAppId = System.getenv("YOUDAO_APP_ID");
            String youdaoAppSecret = System.getenv("YOUDAO_APP_SECRET");
            if (youdaoAppId != null && youdaoAppSecret != null) {
                SysConfig youdaoConfig = new SysConfig()
                    .setProvider("youdao")
                    .setName("有道STT")
                    .setType("stt")
                    .setAppId(youdaoAppId)
                    .setApiSecret(youdaoAppSecret);
                youdaoConfig.setId(7);
                configs.add(youdaoConfig);
            }
        }
        
        return configs;
    }
    
    /**
     * 生成测试报告
     */
    private static void generateTestReport(Map<String, SttAccuracyTest.AccuracyStats> results, String outputFile) {
        try (FileWriter writer = new FileWriter(outputFile)) {
            writer.write("STT准确率测试报告\n");
            writer.write("生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n");
            writer.write("=" + "=".repeat(80) + "\n\n");
            
            // 总体统计
            writer.write("总体统计:\n");
            writer.write("-".repeat(40) + "\n");
            int totalProviders = results.size();
            writer.write(String.format("测试的STT服务数量: %d\n", totalProviders));
            
            if (!results.isEmpty()) {
                int totalFiles = results.values().iterator().next().getTotalFiles();
                writer.write(String.format("测试音频文件数量: %d\n", totalFiles));
            }
            writer.write("\n");
            
            // 各服务详细结果
            writer.write("各服务详细结果:\n");
            writer.write("=" + "=".repeat(80) + "\n");
            
            for (Map.Entry<String, SttAccuracyTest.AccuracyStats> entry : results.entrySet()) {
                String provider = entry.getKey();
                SttAccuracyTest.AccuracyStats stats = entry.getValue();
                
                writer.write(String.format("\n【%s】\n", provider));
                writer.write("-".repeat(40) + "\n");
                writer.write(String.format("总文件数: %d\n", stats.getTotalFiles()));
                writer.write(String.format("成功识别: %d\n", stats.getSuccessfulRecognitions()));
                writer.write(String.format("识别失败: %d\n", stats.getFailedRecognitions()));
                writer.write(String.format("成功率: %.2f%%\n", stats.getSuccessRate()));
                writer.write(String.format("平均准确率: %.2f%%\n", stats.getAverageAccuracy() * 100));
                writer.write(String.format("平均处理时间: %.2f ms\n", stats.getAverageProcessingTime()));
                
                // 详细结果
                writer.write("\n详细结果:\n");
                for (SttAccuracyTest.TestResult result : stats.getResults()) {
                    writer.write(String.format("  文件: %s\n", result.getFileName()));
                    writer.write(String.format("    期望: %s\n", result.getExpectedText()));
                    writer.write(String.format("    识别: %s\n", result.getRecognizedText()));
                    writer.write(String.format("    状态: %s\n", result.isSuccess() ? "成功" : "失败"));
                    writer.write(String.format("    耗时: %d ms\n", result.getProcessingTime()));
                    if (!result.isSuccess() && result.getErrorMessage() != null) {
                        writer.write(String.format("    错误: %s\n", result.getErrorMessage()));
                    }
                    writer.write("\n");
                }
            }
            
            // 性能对比
            writer.write("\n性能对比:\n");
            writer.write("=" + "=".repeat(80) + "\n");
            writer.write(String.format("%-15s %-10s %-12s %-15s %-15s\n", 
                "服务", "成功率", "平均准确率", "平均处理时间", "总处理文件"));
            writer.write("-".repeat(80) + "\n");
            
            for (Map.Entry<String, SttAccuracyTest.AccuracyStats> entry : results.entrySet()) {
                String provider = entry.getKey();
                SttAccuracyTest.AccuracyStats stats = entry.getValue();
                
                writer.write(String.format("%-15s %-10.2f %-12.2f %-15.2f %-15d\n",
                    provider,
                    stats.getSuccessRate(),
                    stats.getAverageAccuracy() * 100,
                    stats.getAverageProcessingTime(),
                    stats.getTotalFiles()));
            }
            
        } catch (IOException e) {
            logger.error("生成测试报告失败", e);
        }
    }
    
    /**
     * 获取命令行参数值
     */
    private static String getArgument(String[] args, String key, String defaultValue) {
        for (int i = 0; i < args.length - 1; i++) {
            if (args[i].equals(key)) {
                return args[i + 1];
            }
        }
        return defaultValue;
    }
    
    /**
     * 检查是否存在指定的命令行参数
     */
    private static boolean hasArgument(String[] args, String key) {
        for (String arg : args) {
            if (arg.equals(key)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 打印使用说明
     */
    public static void printUsage() {
        System.out.println("STT准确率测试工具使用说明:");
        System.out.println("java -cp target/classes com.xiaozhi.dialogue.stt.SttTestRunner [选项]");
        System.out.println();
        System.out.println("选项:");
        System.out.println("  --audio-dir <目录>        指定音频文件目录 (默认: ./audio)");
        System.out.println("  --expected-texts <文件>   指定期望文本文件 (可选)");
        System.out.println("  --output <文件>           指定输出报告文件 (默认: ./stt_test_report.txt)");
        System.out.println("  --enable-vosk             启用Vosk本地识别测试");
        System.out.println("  --enable-all              启用所有可用的STT服务测试");
        System.out.println("  --force-streaming         强制使用流式识别（推荐）");
        System.out.println();
        System.out.println("环境变量配置 (用于云服务API):");
        System.out.println("  TENCENT_SECRET_ID         腾讯云API密钥ID");
        System.out.println("  TENCENT_SECRET_KEY        腾讯云API密钥");
        System.out.println("  ALIYUN_API_KEY            阿里云API密钥");
        System.out.println("  XFYUN_APP_ID              讯飞应用ID");
        System.out.println("  XFYUN_API_KEY             讯飞API密钥");
        System.out.println("  XFYUN_API_SECRET          讯飞API密钥");
        System.out.println("  AZURE_SUBSCRIPTION_KEY    Azure订阅密钥");
        System.out.println("  AZURE_REGION              Azure区域");
        System.out.println("  FUNASR_API_URL            FunASR API地址");
        System.out.println("  DOUBAO_APP_ID             豆包应用ID");
        System.out.println("  DOUBAO_API_KEY            豆包API密钥");
        System.out.println("  YOUDAO_APP_ID             有道应用ID");
        System.out.println("  YOUDAO_APP_SECRET         有道应用密钥");
        System.out.println();
        System.out.println("示例:");
        System.out.println("  java -cp target/classes com.xiaozhi.dialogue.stt.SttTestRunner --audio-dir ./test_audio --enable-vosk");
        System.out.println("  java -cp target/classes com.xiaozhi.dialogue.stt.SttTestRunner --audio-dir ./test_audio --enable-all --output ./report.txt");
    }
}

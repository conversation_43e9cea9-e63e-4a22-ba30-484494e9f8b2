package com.xiaozhi.dialogue.stt;

import com.xiaozhi.dialogue.stt.factory.SttServiceFactory;
import com.xiaozhi.dialogue.stt.SttService;
import com.xiaozhi.entity.SysConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * STT服务简单测试类
 * 传统方式，不依赖任何测试框架
 */
public class SttSimpleTest {
    
    private static final Logger logger = LoggerFactory.getLogger(SttSimpleTest.class);
    
    private final SttServiceFactory sttServiceFactory;
    private final ExecutorService executorService;
    
    public SttSimpleTest() {
        logger.info("初始化STT简单测试...");
        
        // 直接实例化SttServiceFactory
        this.sttServiceFactory = new SttServiceFactory();
        
        // 手动调用初始化方法
        this.sttServiceFactory.initializeDefaultSttService();
        
        // 创建线程池
        this.executorService = Executors.newFixedThreadPool(5);
        
        logger.info("STT简单测试初始化完成");
    }
    
    /**
     * 测试单个STT服务
     */
    public void testSingleService(String provider) {
        logger.info("========== 测试 {} STT服务 ==========", provider);
        
        try {
            // 创建配置
            SysConfig config = new SysConfig();
            config.setProvider(provider);
            config.setId(1);
            
            // 获取服务
            SttService service = sttServiceFactory.getSttService(config);
            logger.info("服务获取成功: {}", service.getClass().getSimpleName());
            logger.info("支持流式识别: {}", service.supportsStreaming());
            
            // 测试音频文件
            String audioDir = "/Users/<USER>/Downloads/uwav";
            File dir = new File(audioDir);
            if (!dir.exists()) {
                logger.warn("音频目录不存在: {}", audioDir);
                return;
            }
            
            File[] audioFiles = dir.listFiles((d, name) -> 
                name.toLowerCase().endsWith(".wav") || 
                name.toLowerCase().endsWith(".pcm"));
            
            if (audioFiles == null || audioFiles.length == 0) {
                logger.warn("未找到音频文件");
                return;
            }
            
            logger.info("找到 {} 个音频文件", audioFiles.length);
            
            // 测试前3个音频文件
            int testCount = Math.min(3, audioFiles.length);
            List<CompletableFuture<TestResult>> futures = new ArrayList<>();
            
            for (int i = 0; i < testCount; i++) {
                File audioFile = audioFiles[i];
                CompletableFuture<TestResult> future = CompletableFuture.supplyAsync(() -> {
                    return testAudioFile(service, audioFile, provider);
                }, executorService);
                futures.add(future);
            }
            
            // 等待所有测试完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(60, TimeUnit.SECONDS);
            
            // 统计结果
            int successCount = 0;
            int totalCount = futures.size();
            long totalTime = 0;
            
            for (CompletableFuture<TestResult> future : futures) {
                TestResult result = future.get();
                if (result.success) {
                    successCount++;
                }
                totalTime += result.processingTime;
            }
            
            double successRate = (double) successCount / totalCount * 100;
            double avgTime = (double) totalTime / totalCount;
            
            logger.info("========== {} 测试结果 ==========", provider);
            logger.info("总文件数: {}", totalCount);
            logger.info("成功识别: {}", successCount);
            logger.info("识别失败: {}", totalCount - successCount);
            logger.info("成功率: {:.1f}%", successRate);
            logger.info("平均处理时间: {:.1f}ms", avgTime);
            logger.info("==========================================");
            
        } catch (Exception e) {
            logger.error("❌ {} 服务测试失败", provider, e);
        }
    }
    
    /**
     * 测试单个音频文件
     */
    private TestResult testAudioFile(SttService service, File audioFile, String provider) {
        TestResult result = new TestResult();
        result.fileName = audioFile.getName();
        result.provider = provider;
        
        try {
            logger.info("测试音频文件: {} ({})", audioFile.getName(), provider);
            
            long startTime = System.currentTimeMillis();
            
            byte[] audioData = Files.readAllBytes(audioFile.toPath());
            logger.info("音频数据大小: {} bytes ({})", audioData.length, provider);
            
            String recognitionResult;
            
            // 优先使用流式识别
            if (service.supportsStreaming()) {
                recognitionResult = testStreamRecognition(service, audioData);
            } else {
                recognitionResult = service.recognition(audioData);
            }
            
            long endTime = System.currentTimeMillis();
            result.processingTime = endTime - startTime;
            
            if (recognitionResult != null && !recognitionResult.trim().isEmpty()) {
                result.success = true;
                result.recognitionText = recognitionResult.trim();
                logger.info("✅ 识别成功 ({}): {} ({}ms)", provider, recognitionResult, result.processingTime);
            } else {
                result.success = false;
                result.errorMessage = "识别结果为空";
                logger.warn("⚠️ 识别结果为空 ({}): {}", provider, audioFile.getName());
            }
            
        } catch (Exception e) {
            result.success = false;
            result.errorMessage = e.getMessage();
            logger.error("❌ 识别失败 ({}): {} - {}", provider, audioFile.getName(), e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 测试流式识别
     */
    private String testStreamRecognition(SttService service, byte[] audioData) throws Exception {
        // 创建音频数据流
        reactor.core.publisher.Sinks.Many<byte[]> audioSink = 
            reactor.core.publisher.Sinks.many().multicast().onBackpressureBuffer();
        
        // 异步调用流式识别
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
            return service.streamRecognition(audioSink);
        });
        
        // 模拟流式发送音频数据
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(500); // 等待连接建立
                
                int chunkSize = 1600; // 100ms音频数据
                for (int i = 0; i < audioData.length; i += chunkSize) {
                    int end = Math.min(i + chunkSize, audioData.length);
                    byte[] chunk = new byte[end - i];
                    System.arraycopy(audioData, i, chunk, 0, end - i);
                    
                    audioSink.tryEmitNext(chunk);
                    Thread.sleep(50); // 模拟实时音频流
                }
                
                audioSink.tryEmitComplete();
                
            } catch (Exception e) {
                logger.error("发送音频数据失败", e);
                audioSink.tryEmitError(e);
            }
        });
        
        // 等待识别结果
        return future.get(30, TimeUnit.SECONDS);
    }
    
    /**
     * 测试所有可用的STT服务
     */
    public void testAllServices() {
        logger.info("开始测试所有STT服务...");
        
        String[] providers = {"vosk", "aliyun", "tencent", "xfyun", "azure", "funasr", "doubao", "youdao"};
        
        for (String provider : providers) {
            try {
                testSingleService(provider);
                Thread.sleep(1000); // 服务间间隔
            } catch (Exception e) {
                logger.error("测试 {} 服务时发生错误", provider, e);
            }
        }
        
        logger.info("所有STT服务测试完成");
    }
    
    /**
     * 关闭资源
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
        }
    }
    
    /**
     * 测试结果类
     */
    private static class TestResult {
        String fileName;
        String provider;
        boolean success;
        String recognitionText;
        String errorMessage;
        long processingTime;
    }
    
    public static void main(String[] args) {
        logger.info("开始STT简单测试...");
        
        SttSimpleTest test = new SttSimpleTest();
        
        try {
            if (args.length > 0) {
                // 测试指定的服务
                String provider = args[0];
                test.testSingleService(provider);
            } else {
                // 测试所有服务
                test.testAllServices();
            }
        } finally {
            test.shutdown();
        }
        
        logger.info("STT简单测试完成");
    }
}

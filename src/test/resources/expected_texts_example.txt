# STT测试期望文本文件
# 格式: 音频文件名<TAB>期望识别文本
# 以#开头的行为注释行

# 示例数据
001_你好世界.wav	你好世界
002_今天天气不错.mp3	今天天气不错
003_我想听音乐.wav	我想听音乐
004_现在几点了.mp3	现在几点了
005_打开灯光.wav	打开灯光
006_关闭空调.mp3	关闭空调
007_播放新闻.wav	播放新闻
008_设置闹钟.mp3	设置闹钟
009_查询天气.wav	查询天气
010_发送消息.mp3	发送消息

# 长句测试
long_001.wav	小智是一个智能语音助手，可以帮助用户完成各种任务
long_002.mp3	请帮我查询明天北京的天气情况，并设置提醒
long_003.wav	我想要播放一些轻松的音乐来放松心情

# 数字和时间测试
number_001.wav	今天是二零二五年十月七日
number_002.mp3	现在是下午三点二十五分
number_003.wav	我的电话号码是一三八零零一二三四五六

# 专业词汇测试
tech_001.wav	人工智能和机器学习正在改变世界
tech_002.mp3	语音识别技术的准确率不断提升
tech_003.wav	深度学习模型在自然语言处理中表现优异

# 方言和口音测试（如果有相关音频）
dialect_001.wav	这个东西很好用
dialect_002.mp3	我觉得没问题
dialect_003.wav	你说得对

# 噪音环境测试（如果有相关音频）
noise_001.wav	在嘈杂环境中的语音识别测试
noise_002.mp3	背景音乐下的语音识别
noise_003.wav	户外环境的语音测试

# 多人对话测试（如果有相关音频）
multi_001.wav	A说你好，B回答你好
multi_002.mp3	会议讨论中的关键信息提取
multi_003.wav	客服对话场景测试

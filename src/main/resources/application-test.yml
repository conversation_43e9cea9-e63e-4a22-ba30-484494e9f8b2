# 开发环境配置

# 数据库连接配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: root
    password: qweQWE!@#
  
  # 使用 Simple 缓存
  cache:
    type: simple

# 开发环境邮箱配置
email:
  smtp:
    username: xxx
    password: xxx


# 小智应用配置
xiaozhi:
  upload:
    path: uploads
  # 服务器地址配置
  server:
    address:
      host: "************"  # 可选：手动指定服务器IP，不配置则自动检测
      # port: 8091             # 可选：手动指定端口，不配置则使用 server.port
      ssl: false               # 是否启用 HTTPS
    websocket:
      path: "/ws/xiaozhi/v1/" # 可选：自定义WebSocket路径
      ssl: false               # 是否启用 WSS
    mqtt:
      host: 0.0.0.0
      port: 1883
      client-publish-topic: device-server
      server-subscribe-topic: device-server-enhance
  # 激活码配置
  activation-code:
    storage-type: mysql        # 存储类型：mysql 或 redis
    validity-hours: 24         # 激活码有效期（小时）
    code-length: 6             # 激活码长度
    code-prefix: ""            # 激活码前缀
    redis:
      key-prefix: "xiaozhi:activation:"  # Redis key 前缀
      cluster-enabled: false   # 是否启用 Redis 集群模式
  mcp:
    device:
      max-tools-count: 32
      timeout-ms: 30000
      auto-discovery: true
    connection:
      timeout-ms: 10000
      retry-count: 3
      retry-interval-ms: 1000
    thirdparty:
      enabled: true
      endpoints: []
  memory:
    type: database

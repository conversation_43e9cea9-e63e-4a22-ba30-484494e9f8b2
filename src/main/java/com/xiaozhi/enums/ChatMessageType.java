package com.xiaozhi.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ChatMessageType {
    Normal("NORMAL"),
    <PERSON><PERSON><PERSON>all("TOOL_CALL"),
    <PERSON><PERSON><PERSON>("SCENARIO");

    @JsonValue
    @EnumValue
    private final String value;

    ChatMessageType(String value) {
        this.value = value;
    }
}

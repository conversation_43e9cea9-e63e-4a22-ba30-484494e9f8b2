package com.xiaozhi.common.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.hivemq.client.mqtt.MqttClient;
import com.hivemq.client.mqtt.mqtt3.Mqtt3AsyncClient;
import com.xiaozhi.communication.server.mqtt.MqttConfig;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.HibernateValidator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.filter.CommonsRequestLoggingFilter;

import java.nio.charset.StandardCharsets;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class BeanCreationConfig {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        var interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

    @Bean
    public BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(10);
    }

    @Bean
    public RestTemplate restTemplate(){
        var restTemplate = new RestTemplate();
        restTemplate.getMessageConverters().add(1,new StringHttpMessageConverter(StandardCharsets.UTF_8));
        return restTemplate;
    }

    @Bean
    public Mqtt3AsyncClient mqttClient(MqttConfig mqttConfig) {
        var clientId = String.format("SERVER-%s", UUID.randomUUID());

        // 使用 HiveMQ MQTT Client 创建异步客户端
        var clientBuilder = MqttClient.builder()
                .useMqttVersion3()
                .identifier(clientId)
                .serverHost(mqttConfig.getBroker().getHost())
                .serverPort(mqttConfig.getBroker().getPort());

        // 如果启用了 SSL，配置 SSL
        if (mqttConfig.getBroker().isSsl()) {
            clientBuilder.sslWithDefaultConfig();
        }

        var mqttClient = clientBuilder.buildAsync();

        try {
            log.info("MQTT 消息处理器连接到 Broker: {}:{}",
                    mqttConfig.getBroker().getHost(), mqttConfig.getBroker().getPort());

            // 构建连接配置
            var connectBuilder = mqttClient.connectWith()
                    .cleanSession(mqttConfig.getBroker().isCleanSession())
                    .keepAlive(mqttConfig.getBroker().getKeepAliveInterval());

            // 设置用户名和密码（如果配置了）
            if (!mqttConfig.getBroker().getUsername().isEmpty()) {
                connectBuilder.simpleAuth()
                        .username(mqttConfig.getBroker().getUsername())
                        .password(mqttConfig.getBroker().getPassword().getBytes(java.nio.charset.StandardCharsets.UTF_8))
                        .applySimpleAuth();
            }

            // 异步连接
            var connectFuture = connectBuilder.send();

            connectFuture.whenComplete((connAck, throwable) -> {
                if (throwable != null) {
                    log.error("MQTT 消息处理器连接失败", throwable);
                } else {
                    log.info("MQTT 消息处理器连接成功，ReturnCode: {}", connAck.getReturnCode());
                }
            });

            // 等待连接完成（最多等待 5 秒）
            connectFuture.get(5, TimeUnit.SECONDS);

        } catch (Exception e) {
            log.error("连接 MQTT Broker 失败", e);
            throw new RuntimeException("连接 MQTT Broker 失败", e);
        }

        return mqttClient;
    }

    @Bean
    public CommonsRequestLoggingFilter requestLoggingFilter() {
        CommonsRequestLoggingFilter loggingFilter = new CommonsRequestLoggingFilter();
        loggingFilter.setIncludeClientInfo(true);
        loggingFilter.setIncludeQueryString(true);
        loggingFilter.setMaxPayloadLength(10000);
        loggingFilter.setIncludePayload(true);
        loggingFilter.setIncludeHeaders(false);
        return loggingFilter;
    }

    @Bean
    public Validator validator() {
        try (var validatorFactory = Validation.byProvider(HibernateValidator.class)
                .configure()
                .failFast(true)
                .buildValidatorFactory()) {
            return validatorFactory.getValidator();
        }
    }

    @Bean
    public OpenAPI apiInfo() {
        var info = new Info().title("XIAOZHI-SERVER").description("The API for xiaozhi-esp32");
        return new OpenAPI().info(info);
    }
}

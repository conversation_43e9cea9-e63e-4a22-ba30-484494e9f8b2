package com.xiaozhi.schedule;

import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.dao.ManagerMapper;
import com.xiaozhi.dao.TaskInstanceMapper;
import com.xiaozhi.dao.TaskMetaMapper;
import com.xiaozhi.entity.Manager;
import com.xiaozhi.entity.TaskInstance;
import com.xiaozhi.entity.TaskMeta;
import com.xiaozhi.enums.TaskStatus;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

@Component
public class CreateTaskInstanceJob {

    @Resource
    private TaskMetaMapper taskMetaMapper;

    @Resource
    private ManagerMapper managerMapper;

    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private final String TaskDelayQueue = "xiaozhi:task:queue";

    @Scheduled(cron = "0 5 0 * * ?")
    public void createTaskInstance() {
        var query = new OhMyLambdaQueryWrapper<TaskMeta>()
                .eq(TaskMeta::getIsEnabled, true);
        var metas = taskMetaMapper.selectList(query);

        // 7 = 周日(位0) … 6 = 周六(位6)
        var bit = LocalDate.now().getDayOfWeek().getValue() % 7;

        for (var meta : metas) {
            // 判断今天要不要跑
            if ((meta.getWeekMask() & (1 << bit)) == 0) continue;

            // 计算下次执行时间
            var fireTime = nextFireTime(meta.getCronExpr());
            if (fireTime == null) continue;

            var managerQuery = new OhMyLambdaQueryWrapper<Manager>()
                    .eq(Manager::getId, meta.getManagerId())
                    .select(Manager::getId, Manager::getDeviceId);
            var manager = managerMapper.selectOne(managerQuery);

            // 创建任务实例
            var instance = new TaskInstance()
                    .setType(meta.getType())
                    .setContent(meta.getContent())
                    .setDuration(meta.getDuration())
                    .setMetaId(meta.getId())
                    .setPlanFireTime(fireTime)
                    .setStatus(TaskStatus.Waiting)
                    .setDeviceId(manager.getDeviceId())
                    .setManagerId(meta.getManagerId());
            taskInstanceMapper.insert(instance);

            stringRedisTemplate.opsForZSet().add(TaskDelayQueue, instance.getId().toString(), fireTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
    }

    /**
     * 根据 cron 计算下一次执行时间（毫秒时间戳）
     */
    private LocalDateTime nextFireTime(String cron) {
        var expr = CronExpression.parse(cron);
        return expr.next(LocalDateTime.now());
    }

}

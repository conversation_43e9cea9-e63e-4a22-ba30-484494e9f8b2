package com.xiaozhi.schedule;

import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.dao.ManagerMapper;
import com.xiaozhi.entity.Manager;
import com.xiaozhi.service.DailyStudyStatsService;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class DailyStudyStatsJob {

    @Resource
    private ManagerMapper managerMapper;

    @Resource
    private DailyStudyStatsService dailyStudyStatsService;

    @Scheduled(cron = "0 55 23 * * ?")
    public void genTodayStats() {
        var query = new OhMyLambdaQueryWrapper<Manager>()
                .select(Manager::getId);

        var managers = managerMapper.selectList(query);

        for (var manager : managers) {
            dailyStudyStatsService.statsOf(manager.getId());
        }
    }

}

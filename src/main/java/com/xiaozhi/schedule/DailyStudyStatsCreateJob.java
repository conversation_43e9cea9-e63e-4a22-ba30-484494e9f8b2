package com.xiaozhi.schedule;

import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.dao.DailyStudyStatsMapper;
import com.xiaozhi.dao.ManagerMapper;
import com.xiaozhi.entity.DailyStudyStats;
import com.xiaozhi.entity.Manager;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

@Component
public class DailyStudyStatsCreateJob {

    @Resource
    private ManagerMapper managerMapper;

    @Resource
    private DailyStudyStatsMapper statsMapper;

    @Scheduled(cron = "5 0 0 * * ?")
    public void genTodayStats() {
        var query = new OhMyLambdaQueryWrapper<Manager>();

        var managers = managerMapper.selectList(query);

        for (var manager : managers) {
            var stats = new DailyStudyStats()
                    .setManagerId(manager.getId())
                    .setDate(LocalDate.now())
                    .setTopics("[]")
                    .setWords("[]")
                    .setSentences("[]")
                    .setGrammars("[]");
            statsMapper.insert(stats);
        }
    }

}

package com.xiaozhi.schedule;

import com.xiaozhi.communication.server.mqtt.MqttServerPublish;
import com.xiaozhi.dao.TaskInstanceMapper;
import com.xiaozhi.enums.TaskStatus;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Component
public class NotifyTaskJob {

    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MqttServerPublish mqttServerPublish;

    private final String TaskDelayQueue = "xiaozhi:task:queue";

    @Scheduled(cron = "0 */1 * * * ?")
    public void run() {
        var endTime = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        var startTime = endTime - 60 * 1000;
        var taskJsonArray = Optional.ofNullable(stringRedisTemplate.opsForZSet().rangeByScore(TaskDelayQueue, startTime, endTime))
                .orElseGet(Set::of);

        log.info("From {} to {} with zone {} get tasks {}", startTime, endTime, ZoneId.systemDefault(), taskJsonArray);

        var tasks = taskJsonArray.stream()
                .map(Integer::valueOf)
                .toList();

        for (var taskId : tasks) {
            var instance = taskInstanceMapper.selectById(taskId);
            if (instance == null) continue;
            if (instance.getStatus() != TaskStatus.Waiting) continue;

            var topic = STR."devices/p2p/GID@@@\{instance.getDeviceId().replaceAll(":", "_")}";
            var title = switch (instance.getType()) {
                case Listening -> "听故事";
                case Information -> "听资讯";
                case Conversation -> "口语对话";
                case Bedtime -> "睡前模式";
            };

            // 唤醒客户端
            // mqttServerPublish.wakeup(topic);
            // 发送任务提醒
            mqttServerPublish.send(topic, STR."""
                            {
                              "type": "task",
                              "title": "\{title}",
                              "task_id": \{taskId}
                            }
                            """);
            // 移除当前任务
            stringRedisTemplate.opsForZSet().remove(TaskDelayQueue, taskId.toString());
        }
    }
}

package com.xiaozhi.dialogue.tts.providers;

import com.xiaozhi.dialogue.tts.Text2SpeechParams;
import com.xiaozhi.dialogue.tts.TtsService;
import org.springframework.ai.openai.api.OpenAiAudioApi;
import org.springframework.stereotype.Service;

@Service
public class OpenAIService implements TtsService {
    @Override
    public String getProviderName() {
        return "openai";
    }

    @Override
    public String textToSpeech(Text2SpeechParams params) throws Exception {
        var audioApi = OpenAiAudioApi.builder()
                .apiKey("")
                .build();

        var response = audioApi.createSpeech(new OpenAiAudioApi.SpeechRequest(
                "gpt-4o-mini-tts",
                params.getText(),
                params.getVoice(),
                OpenAiAudioApi.SpeechRequest.AudioResponseFormat.WAV,
                1.0f
        ));
    }
}

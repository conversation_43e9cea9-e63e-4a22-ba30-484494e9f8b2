package com.xiaozhi.dialogue.llm.memory;

import com.xiaozhi.entity.SysDevice;
import com.xiaozhi.entity.SysMessage;
import com.xiaozhi.enums.ChatMessageType;
import lombok.Getter;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Conversation 是一个 对应于 sys_message 表的，但高于 sys_message 的一个抽象实体。
 * deviceID, roleID, sessionID, 实质构成了一次Conversation的全局唯一ID。这个ID必须final 的。
 * 在关系型数据库里，可以将deviceID, roleID, sessionID 建一个组合索引，注意顺序sessionID放在最后。
 * 在图数据库里， conversation label的节点，连接 device节点、role节点。
 * deviceID与roleID本质上不是Conversation的真正属性，而是外键，代表连接的2个对象。
 * 只有sessionID是真正挂在Conversation的属性。
 *
 */
public class Conversation {
    private final SysDevice device;
    private final String sessionId;
    @Getter
    private final Integer roleId;

    protected String systemPrompt;

    protected List<Message> messages;

    public Conversation(SysDevice device, Integer roleId, String systemPrompt, String sessionId) {
        // final 属性的规范要求
        Assert.notNull(device, "device must not be null");
        Assert.notNull(roleId, "roleId must not be null");
        Assert.notNull(device.getDeviceId(), "deviceId must not be null");
        Assert.notNull(systemPrompt, "systemPrompt must not be null");
        Assert.notNull(sessionId, "sessionId must not be null");
        this.device = device;
        this.roleId = roleId;
        this.systemPrompt = systemPrompt;
        this.sessionId = sessionId;
    }

    public SysDevice device() {
        return device;
    }

    public String sessionId() {
        return sessionId;
    }

    public List<Message> messages() {
        return messages;
    }

    public void clear() {
        messages.clear();
    }

    public void addMessage(UserMessage userMessage, Long userTimeMillis, AssistantMessage assistantMessage,
            Long assistantTimeMillis) {
        messages.add(userMessage);
        messages.add(assistantMessage);
    }

    public void addMessage(UserMessage userMessage, Long userTimeMillis, AssistantMessage assistantMessage,
                           Long assistantTimeMillis, ChatMessageType type) {
        messages.add(userMessage);
        messages.add(assistantMessage);
    }

    /**
     * 获取适用于放入prompt提示词的多轮消息列表。
     * userMessage 不会因调用此方法而入库（或进入记忆）
     *
     * @param userMessage 必须且不为空。
     * @return 新的消息列表对象，避免污染原有的列表。
     */
    public List<Message> prompt(UserMessage userMessage) {
        var newMessages = new ArrayList<>(this.messages);
        newMessages.add(userMessage);
        return newMessages;
    }

    public List<Message> prompt(AssistantMessage assistantMessage, UserMessage userMessage) {
        var newMessages = new ArrayList<>(this.messages);
        newMessages.add(assistantMessage);
        newMessages.add(userMessage);
        return newMessages;
    }

    public List<Message> promptWith(Map<String, Object> variables) {
        var systemPrompt = new PromptTemplate(this.systemPrompt)
                .create(variables);

        return new ArrayList<>(this.messages);
    }

    public void loadMemory(Map<String, Object> vars) {
        messages = new ArrayList<>();
    }

    /**
     * 将数据库记录的 SysMessage 转换为 spring-ai 的 Message
     */
    public static List<Message> convert(List<SysMessage> messages) {
        if (messages == null || messages.isEmpty()) {
            return Collections.emptyList();
        }
        return messages.stream()
                .map(message -> {
                    return switch (message.getSender()) {
                        case "user" -> new UserMessage(message.getContent());
                        case "assistant" -> new AssistantMessage(message.getContent());
                        default -> null;
                    };
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}

package com.xiaozhi.dialogue.llm.memory;

import com.xiaozhi.dialogue.llm.factory.ChatModelFactory;
import com.xiaozhi.entity.SysDevice;
import com.xiaozhi.entity.SysRole;
import jakarta.annotation.Resource;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnProperty(name = "xiaozhi.memory.type", havingValue = "summary")
public class SummaryConversationFactory implements ConversationFactory {

    @Resource
    private ChatMemory chatMemory;

    @Resource
    private ChatModelFactory chatModelFactory;

    @Override
    public Conversation initConversation(SysDevice device, SysRole role, String sessionId) {
        return SummaryConversation.builder()
                .role(role)
                .device(device)
                .maxMessages(20)
                .sessionId(sessionId)
                .chatMemory(chatMemory)
                .chatModel(chatModelFactory.takeModelFor("chat"))
                .build();
    }
}

package com.xiaozhi.dialogue.llm.memory;

import com.xiaozhi.entity.SysDevice;
import com.xiaozhi.entity.SysRole;
import jakarta.annotation.Resource;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnProperty(name = "xiaozhi.memory.type", havingValue = "database")
public class WindowConversationFactory implements ConversationFactory {

    @Resource
    private ChatMemory chatMemory;

    @Override
    public Conversation initConversation(SysDevice device, SysRole role, String sessionId) {
        return MessageWindowConversation.builder()
                .chatMemory(chatMemory)
                .maxMessages(10)
                .role(role)
                .device(device)
                .sessionId(sessionId)
                .build();
    }
}

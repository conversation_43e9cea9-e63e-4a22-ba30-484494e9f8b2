package com.xiaozhi.dialogue.llm.tool.function;

import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.llm.ChatService;
import com.xiaozhi.dialogue.llm.tool.ToolsGlobalRegistry;
import com.xiaozhi.dialogue.llm.tool.ToolCallStringResultConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class StopChatFunction implements ToolsGlobalRegistry.GlobalFunction {

    ToolCallback toolCallback = FunctionToolCallback
            .builder("func_stop_chat", (Map<String, String> _, ToolContext toolContext) -> {
                var chatSession = (ChatSession) toolContext.getContext().get(ChatService.TOOL_CONTEXT_SESSION_KEY);
                log.info("Close is {}", chatSession.isCloseAfterChat());
                chatSession.setCloseAfterChat(true);
                log.info("Close is {}", chatSession.isCloseAfterChat());
                return "";
            })
            .toolMetadata(ToolMetadata.builder().returnDirect(true).build())
            .description("Provides the ability to end a conversation")
            .inputSchema("""
                        {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }
                    """)
            .inputType(Map.class)
            .toolCallResultConverter(ToolCallStringResultConverter.INSTANCE)
            .build();

    @Override
    public ToolCallback getFunctionCallTool(ChatSession chatSession) {
        return toolCallback;
    }
}

package com.xiaozhi.dialogue.llm.tool.function;

import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.llm.tool.ToolCallStringResultConverter;
import com.xiaozhi.dialogue.llm.tool.ToolsGlobalRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.stereotype.Component;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

@Slf4j
@Component
public class GetTimeFunction implements ToolsGlobalRegistry.GlobalFunction {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    ToolCallback toolCallback = FunctionToolCallback
            .builder("func_get_time", (Map<String, Double> _, ToolContext _) -> {
                var time = formatter.format(ZonedDateTime.now(ZoneId.of("Asia/Shanghai")));
                log.info("Call func_get_time tool, result is {}", time);
                return time;
            })
            .toolMetadata(ToolMetadata.builder().returnDirect(false).build())
            .description("Provides current China Standard Time")
            .inputSchema("""
                    {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                    """)
            .inputType(Map.class)
            .toolCallResultConverter(ToolCallStringResultConverter.INSTANCE)
            .build();

    @Override
    public ToolCallback getFunctionCallTool(ChatSession chatSession) {
        return toolCallback;
    }
}

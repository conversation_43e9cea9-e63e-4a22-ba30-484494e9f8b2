package com.xiaozhi.dialogue.llm.tool.mcp.thirdparty;

import com.xiaozhi.config.McpConfig;
import com.xiaozhi.dialogue.llm.tool.mcp.McpUtil;
import com.xiaozhi.utils.JsonUtil;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.spec.McpSchema;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Third-party MCP service for accessing external Model Context Protocol endpoints
 */
@Service
public class ThirdPartyMcpService {
    private static final Logger logger = LoggerFactory.getLogger(ThirdPartyMcpService.class);

    @Resource
    private McpConfig mcpConfig;

    // Store connection info for endpoints that have been tested successfully
    private final Map<String, ConnectionInfo> validatedEndpoints = new ConcurrentHashMap<>();

    /**
     * Connection information for validated endpoints
     */
    private static class ConnectionInfo {
        final String endpointUrl;
        final Map<String, String> headers;

        ConnectionInfo(String endpointUrl, Map<String, String> headers) {
            this.endpointUrl = endpointUrl;
            this.headers = headers != null ? new HashMap<>(headers) : new HashMap<>();
        }
    }

    /**
     * Connect to a third-party MCP endpoint and register its tools
     *
     * @param endpointUrl The URL of the third-party MCP endpoint
     * @param headers     Optional headers to include in requests
     * @return A list of registered tool callbacks
     */
    public List<FunctionToolCallback> connectAndRegisterTools(String endpointUrl, Map<String, String> headers) {
        try {
            var transport = McpUtil.createTransport(endpointUrl, headers);
            if (transport == null) {
                return List.of();
            }

            // Test connection and get tools
            try (var client = McpClient.sync(transport).build()) {
                var initializeResult = client.initialize();
                if (initializeResult == null) {
                    logger.error("Failed to initialize MCP connection to {}", endpointUrl);
                    return List.of();
                }

                client.ping();
                var listToolsResult = client.listTools();

                if (listToolsResult == null || listToolsResult.tools() == null) {
                    logger.warn("No tools returned from MCP endpoint: {}", endpointUrl);
                    return List.of();
                }

                // Store connection info for validated endpoint
                validatedEndpoints.put(endpointUrl, new ConnectionInfo(endpointUrl, headers));

                // Create tool callbacks
                return listToolsResult.tools().stream()
                        .map(tool -> createToolCallback(endpointUrl, headers, tool))
                        .toList();
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Error connecting to MCP endpoint {}: {}", endpointUrl, e.getMessage());
            return List.of();
        }
    }

    /**
     * Create a tool callback that will create MCP client on demand
     */
    private FunctionToolCallback createToolCallback(String endpointUrl, Map<String, String> headers, McpSchema.Tool tool) {
        String name = tool.name();
        String description = tool.description();
        var inputSchema = tool.inputSchema();

        return FunctionToolCallback.builder(STR."mcp_\{name}", (Map<String, Object> params, ToolContext _) -> callToolOnDemand(endpointUrl, headers, name, params))
                .toolMetadata(ToolMetadata.builder().returnDirect(false).build())
                .description(description != null ? description : STR."MCP tool: \{name}")
                .inputSchema(JsonUtil.toJson(inputSchema))
                .inputType(Map.class)
                .build();
    }

    /**
     * Call a tool on demand by creating a new MCP client
     */
    private String callToolOnDemand(String endpointUrl, Map<String, String> headers, String toolName, Map<String, Object> params) {
        try {
            // Get validated connection info
            ConnectionInfo connectionInfo = validatedEndpoints.get(endpointUrl);
            if (connectionInfo == null) {
                logger.warn("Endpoint {} not found in validated endpoints", endpointUrl);
                return "Endpoint not validated";
            }

            // Use stored headers from validated connection
            var transport = McpUtil.createTransport(endpointUrl, connectionInfo.headers);
            if (transport == null) {
                return "Failed to call %s".formatted(toolName);
            }

            try (var client = McpClient.sync(transport).build()) {
                // Initialize connection
                var initializeResult = client.initialize();
                if (initializeResult == null) {
                    return "Failed to initialize MCP connection";
                }
                var result = client.callTool(new McpSchema.CallToolRequest(toolName, params));

                // Since there's no direct callTool method, we need to implement the MCP protocol manually
                // For now, return a placeholder response
                logger.info("Tool {} called with params: {} on endpoint: {}", toolName, params, endpointUrl);

                logger.info("Tool Resp {}", result);

                if (result.isError()) {
                    return "调用 MCP 功能失败";
                }

                return result.content().stream().map(it -> switch (it) {
                    case McpSchema.TextContent textContent -> textContent.text();
                    default -> "暂不支持的结果";
                }).collect(Collectors.joining(""));
            }

        } catch (Exception e) {
            logger.error("Error calling tool {} on MCP endpoint {}: {}", toolName, endpointUrl, e.getMessage());
            return "Failed to call tool: " + e.getMessage();
        }
    }

    /**
     * Extract property from tool object using reflection
     */
    private String extractToolProperty(Object tool, String propertyName) {
        try {
            var method = tool.getClass().getMethod(propertyName);
            Object result = method.invoke(tool);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            logger.warn("Failed to extract property {} from tool: {}", propertyName, e.getMessage());
            return null;
        }
    }

    /**
     * Disconnect from an MCP endpoint (remove from validated endpoints)
     */
    public boolean disconnectFromEndpoint(String endpointUrl) {
        boolean wasValidated = validatedEndpoints.remove(endpointUrl) != null;
        if (wasValidated) {
            logger.debug("Removed MCP endpoint from validated list: {}", endpointUrl);
            return true;
        } else {
            logger.warn("Attempted to disconnect from non-validated MCP endpoint: {}", endpointUrl);
            return false;
        }
    }

    /**
     * Check if endpoint has been validated (connection tested successfully)
     */
    public boolean isConnectedToEndpoint(String endpointUrl) {
        return validatedEndpoints.containsKey(endpointUrl);
    }

    /**
     * Get all validated endpoints
     */
    public java.util.Set<String> getConnectedEndpoints() {
        return new java.util.HashSet<>(validatedEndpoints.keySet());
    }
}
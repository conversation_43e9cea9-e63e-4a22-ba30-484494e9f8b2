package com.xiaozhi.dialogue.service;

import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.communication.common.SessionManager;
import com.xiaozhi.communication.domain.TaskChain;
import com.xiaozhi.communication.server.mqtt.MqttServerPublish;
import com.xiaozhi.dao.ManagerMapper;
import com.xiaozhi.dao.MediaMapper;
import com.xiaozhi.dao.TaskInstanceMapper;
import com.xiaozhi.dialogue.domain.StartParams;
import com.xiaozhi.entity.Manager;
import com.xiaozhi.entity.TaskInstance;
import com.xiaozhi.enums.TaskContent;
import com.xiaozhi.enums.TaskStatus;
import com.xiaozhi.utils.CozeUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class TaskExecService {

    @Resource
    private ManagerMapper managerMapper;

    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Resource
    private SessionManager sessionManager;

    @Resource
    private DialogueService dialogueService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MqttServerPublish mqttServerPublish;

    @Resource
    private MediaMapper mediaMapper;

    @Resource
    private AudioPushService audioPushService;

    public void processTask(Integer taskId, Integer action) {
        var instance = taskInstanceMapper.selectById(taskId);
        if (instance == null) return;
        if (instance.getStatus() != TaskStatus.Waiting) return;

        switch (action){
            case 0 -> {
                instance.setStatus(TaskStatus.Canceled);
                taskInstanceMapper.updateById(instance);
            }
            case 1 -> run(instance);
            case 2 -> {
                log.info("Curr time is {}, {}", LocalDateTime.now(), LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli());
                var now = LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli();
                var delayed = now + 4 * 60 * 1000;
                log.info("Delay task {} at {}", taskId, delayed);
                stringRedisTemplate.opsForZSet().add("xiaozhi:task:queue", taskId.toString(), delayed);
            }
        }
    }

    private void run(TaskInstance instance) {
        // create task chain
        var durations = new ArrayList<Integer>();
        for (int i = 0; i < instance.getDuration().length(); i += 2) {
            durations.add(Integer.parseInt(instance.getDuration().substring(i, i + 2)));
        }

        var now = Instant.now();
        var dummy = new TaskChain();
        var idx = new AtomicInteger(0);
        Arrays.stream(TaskContent.values())
                .filter(it -> (it.getValue() & instance.getContent()) != 0)
                .map(it -> new TaskChain().setInstanceId(instance.getId()).setContent(it).setStartTime(now).setDuration(durations.get(idx.getAndIncrement())))
                .reduce(dummy, (z, x) -> {
                    z.setNext(x);
                    return x;
                }, (_, it) -> it);

        var taskChain = dummy.getNext();
        var topic = STR."devices/p2p/GID@@@\{instance.getDeviceId().replaceAll(":", "_")}";

        var session = sessionManager.getSessionByDeviceId(instance.getDeviceId());
        if (session != null && session.isPlaying()) {
            session.setIsCurrTaskDone(false);
            return;
        }

        switch (instance.getType()) {
            case Listening -> {
                log.info("Start Listening task");
                mqttServerPublish.open(topic, "player", "story");
            }

            case Information -> {
                log.info("Start Information task");
                mqttServerPublish.open(topic, "player", "news");
            }
            case Bedtime, Conversation -> {
                mqttServerPublish.wakeup(topic);
                Thread.startVirtualThread(() -> {
                    try {
                        TimeUnit.SECONDS.sleep(2);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                    var newSession = sessionManager.getSessionByDeviceId(instance.getDeviceId());
                    if (newSession == null) return;
                    newSession.setTaskChain(taskChain);

                    if (newSession.isPlaying()) {
                        // 设备正在播放，将任务置为取消
                        instance.setStatus(TaskStatus.Canceled);
                        taskInstanceMapper.updateById(instance);
                        return;
                    }

                    var managerQuery = new OhMyLambdaQueryWrapper<Manager>()
                            .eq(Manager::getId, instance.getManagerId())
                            .select(Manager::getId, Manager::getName, Manager::getCefr, Manager::getStarterConvId);
                    var manager = managerMapper.selectOne(managerQuery);

                    var startParams = new StartParams()
                            .setScene("【】")
                            .setName(manager.getName())
                            .setCefr(manager.getCefr())
                            .setUserId(manager.getId().toString())
                            .setConversationId(manager.getStarterConvId());
                    var hello = CozeUtil.genStartSentence(startParams);
                    dialogueService.sendOneSentence(newSession, hello, true)
                            .thenRun(() -> {
                                instance.setStatus(TaskStatus.Running);
                                taskInstanceMapper.updateById(instance);
                            });
                });

            }
        }
    }

}

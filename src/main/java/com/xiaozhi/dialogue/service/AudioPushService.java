package com.xiaozhi.dialogue.service;

import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.utils.AudioUtils;
import com.xiaozhi.utils.OpusProcessor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class AudioPushService {

    @Resource
    private OpusProcessor opusProcessor;

    // 使用虚拟线程池处理定时任务
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(
            Runtime.getRuntime().availableProcessors(),
            Thread.ofVirtual().name("audio-push-scheduler-", 0).factory());

    // 音频发送相关常量
    private static final long OPUS_FRAME_SEND_INTERVAL_MS = AudioUtils.OPUS_FRAME_DURATION_MS;
    private static final int PRE_BUFFER_FRAMES = 3;

    /**
     * 发送音频消息（基于AudioService实现）
     *
     * @param session   ChatSession会话
     * @param audioPath 音频文件路径
     * @param text      文本内容
     * @param isFirst   是否第一句
     * @param isLast    是否最后一句
     * @return 操作完成的CompletableFuture
     */
    public CompletableFuture<Void> sendAudioMessage(ChatSession session, String audioPath, String text, boolean isFirst,
                                                    boolean isLast) {
        String sessionId = session.getSessionId();

        // 初始化播放状态
        session.setAudioPlaying(true);
        session.setLastActivityTime(Instant.now());

        var filepath = downloadIfNeed(audioPath);

        return sendMessageHeaders(session, text, isFirst)
                .thenCompose(v -> processAudioContent(session, filepath))
                .whenComplete((result, error) -> finalizePlayback(session, sessionId))
                .thenCompose(v -> sendStopIfNeeded(session, isLast))
                .exceptionally(error -> handleSendError(session, isLast, error));
    }

    /**
     * 发送消息头部信息（TTS开始、句子开始）
     */
    private CompletableFuture<Void> sendMessageHeaders(ChatSession session, String text, boolean isFirst) {
        CompletableFuture<Void> startFuture = isFirst
                ? CompletableFuture.runAsync(session::sendTTSStart)
                : CompletableFuture.completedFuture(null);

        return startFuture
                .thenRun(() -> session.sendSentenceStart(text));
    }

    /**
     * 处理音频内容
     */
    private CompletableFuture<Void> processAudioContent(ChatSession session, String audioPath) {
        if (audioPath == null) {
            session.setAudioPlaying(false);
            return CompletableFuture.completedFuture(null);
        }

        return loadAudioFrames(session.getSessionId(), audioPath)
                .thenCompose(frames -> transmitAudioFrames(session, frames));
    }

    /**
     * 加载音频帧数据
     */
    private CompletableFuture<List<byte[]>> loadAudioFrames(String sessionId, String audioPath) {
        return CompletableFuture.supplyAsync(() -> {
            var audioFile = new File(audioPath);
            if (!audioFile.exists()) {
                log.warn("音频文件不存在: {}", audioPath);
                return null;
            }

            try {
                var lowerPath = audioPath.toLowerCase();
                if (lowerPath.contains(".opus") || lowerPath.endsWith(".ogg")) {
                    // 处理 Opus 文件（包括带 OGG 容器的）
                    var frames = opusProcessor.readOpus(audioFile);
                    if (frames == null || frames.isEmpty()) {
                        log.warn("无法从 Opus/OGG 文件读取音频帧: {}", audioPath);
                        return null;
                    }
                    log.info("成功加载 {} 个 Opus 音频帧", frames.size());
                    return frames;
                } else {
                    // 处理其他格式，转换为 PCM 再编码为 Opus
                    var audioData = AudioUtils.readAsPcm(audioPath);
                    if (audioData == null || audioData.length == 0) {
                        log.warn("无法读取音频数据: {}", audioPath);
                        return null;
                    }
                    return opusProcessor.pcmToOpus(sessionId, audioData, false);
                }
            } catch (Exception e) {
                log.error("处理音频文件失败: {}", audioPath, e);
                return null;
            }
        });
    }

    /**
     * 传输音频帧数据
     */
    private CompletableFuture<Void> transmitAudioFrames(ChatSession session, List<byte[]> opusFrames) {
        if (opusFrames == null || opusFrames.isEmpty()) {
            session.setAudioPlaying(false);
            return CompletableFuture.completedFuture(null);
        }

        session.setAudioPlaying(true);

        CompletableFuture<Void> transmissionFuture = new CompletableFuture<>();

        long startTime = System.nanoTime();
        try {
            sendPreBufferedFrames(session, opusFrames);
            scheduleRemainingFrames(session, opusFrames, startTime, transmissionFuture);
        } catch (Exception e) {
            log.error("音频帧传输初始化失败", e);
            transmissionFuture.completeExceptionally(e);
        }

        return transmissionFuture;
    }

    /**
     * 发送预缓冲帧
     */
    private void sendPreBufferedFrames(ChatSession session, List<byte[]> opusFrames) throws IOException {
        int preBufferCount = Math.min(PRE_BUFFER_FRAMES, opusFrames.size());
        for (int i = 0; i < preBufferCount; i++) {
            sendOpusFrame(session, opusFrames.get(i));
        }
    }

    /**
     * 调度剩余帧的发送
     */
    private void scheduleRemainingFrames(ChatSession session, List<byte[]> opusFrames, long startTime,
                                         CompletableFuture<Void> future) {
        int startFrameIndex = Math.min(PRE_BUFFER_FRAMES, opusFrames.size());

        if (startFrameIndex >= opusFrames.size()) {
            future.complete(null);
            return;
        }

        scheduleFrameTransmission(session, opusFrames, startFrameIndex, startTime, future);
    }

    /**
     * 调度单个帧的传输
     */
    private void scheduleFrameTransmission(ChatSession session, List<byte[]> opusFrames,
                                           int frameIndex, long startTime, CompletableFuture<Void> future) {

        session.setLastActivityTime(Instant.now());
        Runnable frameTask = () -> {
            try {
                if (!session.isAudioPlaying() || frameIndex >= opusFrames.size() || !session.isOpen()) {
                    future.complete(null);
                    return;
                }

                sendOpusFrame(session, opusFrames.get(frameIndex));

                var nextFrameIndex = frameIndex + 1;
                if (nextFrameIndex < opusFrames.size()) {
                    scheduleFrameTransmission(session, opusFrames, nextFrameIndex, startTime, future);
                } else {
                    future.complete(null);
                }
            } catch (Exception e) {
                log.error("帧发送失败", e);
                future.completeExceptionally(e);
            }
        };

        scheduleNextFrame(frameIndex, startTime, frameTask);
    }

    /**
     * 发送Opus帧数据
     */
    private void sendOpusFrame(ChatSession session, byte[] opusFrame) throws IOException {
        session.sendBinaryMessage(opusFrame);
    }

    /**
     * 完成播放处理
     */
    private void finalizePlayback(ChatSession session, String sessionId) {
        session.setAudioPlaying(false);
    }

    /**
     * 根据需要发送停止信号
     */
    private CompletableFuture<Void> sendStopIfNeeded(ChatSession session, boolean isLast) {
        if (isLast) {
            return CompletableFuture.runAsync(session::sendTTSStop);
        }
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 处理发送错误
     */
    private Void handleSendError(ChatSession session, boolean isLast, Throwable error) {
        log.error("音频发送失败", error);
        session.setAudioPlaying(false);
        if (isLast) {
            session.sendTTSStop();
        }
        return null;
    }

    /**
     * 计算并调度下一帧的发送时间
     */
    private void scheduleNextFrame(int frameIndex, long startTime, Runnable frameTask) {
        // 直接用帧索引计算预期发送时间（纳秒级精度）
        long expectedTime = startTime + frameIndex * OPUS_FRAME_SEND_INTERVAL_MS * 1_000_000;
        long currentTime = System.nanoTime();
        long delayNanos = expectedTime - currentTime;

        if (delayNanos <= 0) {
            // 如果当前时间已经超过预期时间，立即发送
            scheduler.schedule(frameTask, 0, TimeUnit.NANOSECONDS);
        } else {
            // 延迟到精确时间点再发送
            scheduler.schedule(frameTask, delayNanos, TimeUnit.NANOSECONDS);
        }
    }

    /**
     * 清理会话资源（公共接口）
     */
    public void cleanupSession(String sessionId) {
        opusProcessor.cleanup(sessionId);
    }

    /**
     * 直接发送音频帧数据
     *
     * @param session    ChatSession会话
     * @param opusFrames Opus音频帧列表
     * @param text       文本内容
     * @param isFirst    是否第一句
     * @param isLast     是否最后一句
     * @return 操作完成的CompletableFuture
     */
    public CompletableFuture<Void> sendAudioFrames(ChatSession session, List<byte[]> opusFrames, String text,
                                                   boolean isFirst, boolean isLast) {
        String sessionId = session.getSessionId();

        // 初始化播放状态
        session.setAudioPlaying(true);
        session.setLastActivityTime(Instant.now());

        return sendMessageHeaders(session, text, isFirst)
                .thenCompose(v -> transmitAudioFrames(session, opusFrames))
                .whenComplete((result, error) -> finalizePlayback(session, sessionId))
                .thenCompose(v -> sendStopIfNeeded(session, isLast))
                .exceptionally(error -> handleSendError(session, isLast, error));
    }

    private String downloadIfNeed(String fileUrl) {
        if (fileUrl.startsWith("http")) {
            var uri = URI.create(fileUrl);
            var segments = uri.getPath().split("/");
            var filename = segments[segments.length - 1];
            var localPath = Paths.get("audio", filename);
            if (Files.notExists(localPath)) {
                try (var is = uri.toURL().openStream()) {
                    Files.copy(is, localPath, StandardCopyOption.REPLACE_EXISTING);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            return localPath.toString();
        }

        return fileUrl;
    }
}

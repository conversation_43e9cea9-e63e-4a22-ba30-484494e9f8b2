package com.xiaozhi.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaozhi.dto.MessageDuration;
import com.xiaozhi.entity.SysMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 聊天记录 数据层
 * 
 * <AUTHOR>
 * 
 */
@Mapper
public interface MessageMapper extends BaseMapper<SysMessage> {

    @Select("SELECT MAX(id) AS id, MIN(created_at) AS begin, MAX(created_at) AS end FROM sys_message ${ew.customSqlSegment}")
    List<MessageDuration> findDuration(@Param(Constants.WRAPPER) Wrapper<SysMessage> wrapper);

    @Select("SELECT id FROM sys_message WHERE device_id = #{deviceId} AND is_deleted = 0 ORDER BY ID DESC LIMIT 1")
    Integer findLastMsg(@Param("deviceId") String deviceId);

}
package com.xiaozhi.utils;

import com.xiaozhi.dialogue.domain.StartParams;
import com.xiaozhi.dialogue.llm.providers.CozeChatModel;
import io.vavr.control.Try;

import java.util.Map;

public class CozeUtil {

    private static final String SECRET = "sat_DKYZIpfVz7NPw3i3U88msfgwWye4uZbtCn4M1tZkC94nIG9wIVnKjtbdiUA3Yzbq";

    public static String genStartSentence(StartParams params) {
        var chatModel = new CozeChatModel("7532759572071792674", SECRET);
        if (params.getConversationId() == null) {
            var convId = chatModel.getConversationId(null);
            params.setConversationId(convId);
        }
        return chatModel.callWithVars(params.getScene(), params.getConversationId(), Map.of(
                "nickname", params.getName(),
                "level", params.getCefr()
        ));
    }

    public static String getStarterConvId() {
        var chatModel = new CozeChatModel("7532759572071792674", SECRET);
        return chatModel.getConversationId(null);
    }

    public static Try<Content> genReadingContent(String topic, String category) {
        var botId = category.equals("news") ? "7537147450772373544" : "7542792580560863295";
        var chatModel = new CozeChatModel(botId, SECRET);
        var resp = chatModel.call(topic);

        return JsonUtil.parse(resp, Content.class);
    }

    public record Content(String story, String cover) {
    }
}

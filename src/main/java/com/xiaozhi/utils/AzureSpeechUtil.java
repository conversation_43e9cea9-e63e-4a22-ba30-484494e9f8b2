package com.xiaozhi.utils;

import com.microsoft.cognitiveservices.speech.*;
import com.microsoft.cognitiveservices.speech.audio.AudioConfig;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

@Slf4j
@Component
public class AzureSpeechUtil {

    private static final String endpoint = "https://southeastasia.tts.speech.microsoft.com";
    private static final String secret = "1b8HjkGguTG4OFOk8SeWljidFzddhVPGWGLYSU93zjaLKhW2dUwpJQQJ99BHACqBBLyXJ3w3AAAAACOGuoH2";

    public static Try<String> speak(String content, String voice) {
        return Try.of(() -> SpeechConfig.fromEndpoint(new URI(endpoint), secret))
                .flatMap(config -> {
                    config.setSpeechSynthesisVoiceName(voice);
                    config.setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat.Audio24Khz48KBitRateMonoMp3);
                    var filepath = STR."audio/\{UUID.randomUUID().toString()}.mp3";
                    try (var synthesizer = new SpeechSynthesizer(config, AudioConfig.fromWavFileOutput(filepath))) {
                        return Try.of(() -> synthesizer.SpeakText(content))
                                .onFailure(Throwable::printStackTrace)
                                .filter(it -> it.getReason() == ResultReason.SynthesizingAudioCompleted)
                                .onFailure(Throwable::printStackTrace)
                                .map(result -> {
                                    result.close();
                                    return filepath;
                                });
                    }
                });
    }

    public static Try<String> speakSsml(String content, String voice, String lang) {
        return Try.of(() -> SpeechConfig.fromEndpoint(new URI(endpoint), secret))
                .flatMap(config -> {
                    config.setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat.Audio24Khz48KBitRateMonoMp3);
                    var ssmlText = buildSSML(removeSpeakTags(content), voice, lang);
                    var filepath = STR."audio/\{UUID.randomUUID().toString()}.mp3";
                    try (var synthesizer = new SpeechSynthesizer(config, AudioConfig.fromWavFileOutput(filepath))) {
                        return Try.of(() -> synthesizer.SpeakSsml(ssmlText))
                                .onFailure(Throwable::printStackTrace)
                                .filter(it -> it != null && it.getReason() == ResultReason.SynthesizingAudioCompleted)
                                .map(result -> {
                                    result.close();
                                    return filepath;
                                });
                    }
                });
    }

    public static String buildSSML(String text, String voice, String lang) {
        return STR."""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="\{lang}">
                <voice name="\{voice}">
                    \{text}
                </voice>
            </speak>
            """;
    }

    private static String removeSpeakTags(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 移除开头的 <speak> 标签和结尾的 </speak> 标签
        var result = input.replaceAll("(?i)<speak[^>]*>", "")
                .replaceAll("(?i)</speak>", "");

        return result.trim();
    }


    static void main() {
        var text = """
                <speak>
                小朋友们，你们有没有发现，有些植物一到晚上叶子就像人闭上眼睡觉一样合起来啦，这是为啥呀？<break time="500ms"/>
                其实啊，植物晚上把叶片闭合起来，就像给自己裹上了一层小毛毯。<break time="300ms"/>夜晚气温变低，叶片闭合能减少热量散失，就像我们冬天盖被子保暖一样。<break time="400ms"/>比如说<emphasis>三叶草</emphasis>，它的三片叶子到了晚上就紧紧合在一起，就像三个好朋友抱成团取暖。<break time="300ms"/>这里“叶片”的英文是“<say-as interpret-as="spell-out">leaf</say-as>”，大家跟着我念，<prosody rate="slow" pitch="high">leaf</prosody>，再念一遍，<prosody rate="slow" pitch="high">leaf</prosody>。<break time="500ms"/>
                而且呀，叶片闭合还能保护植物不被<emphasis>害虫</emphasis>伤害。<break time="300ms"/>有些小虫子喜欢在晚上出来找吃的，当植物叶片闭合起来，小虫子就很难下嘴，这就好比植物给自己建了一座保护城堡。<break time="400ms"/>像<emphasis>合欢树</emphasis>，它的叶子晚上一闭合，那些想吃叶子的小虫子就无从下口咯。<break time="300ms"/>“害虫”的英文是“<say-as interpret-as="spell-out">bug</say-as>”，大家一起读，<prosody rate="slow" pitch="high">bug</prosody>，再来一次，<prosody rate="slow" pitch="high">bug</prosody>。<break time="500ms"/>
                植物的这种“睡眠”现象和它体内的<emphasis>生物钟</emphasis>有关。<break time="300ms"/>生物钟就像一个小闹钟，告诉植物什么时候该“睡觉”，什么时候该“起床”。<break time="400ms"/>白天的时候，<emphasis>太阳</emphasis>出来了，植物就展开叶片，像伸个懒腰开始工作，吸收阳光制造养分。<break time="400ms"/>到了晚上，太阳下山，生物钟就提醒植物把叶片闭合，进入休息状态。<break time="300ms"/>“太阳”的英文是“<say-as interpret-as="spell-out">sun</say-as>”，跟我读，<prosody rate="slow" pitch="high">sun</prosody>，再读一遍，<prosody rate="slow" pitch="high">sun</prosody>。<break time="500ms"/>
                不同的植物“睡觉”的样子也不一样。<break time="300ms"/>有的植物是像折扇一样把叶片慢慢收拢，有的则是两片叶子对折起来。<break time="400ms"/>比如<emphasis>花生</emphasis>，它的叶子晚上会两两相对闭合，就像在说悄悄话。<break time="300ms"/>“花生”的英文是“<say-as interpret-as="spell-out">peanut</say-as>”，大家念，<prosody rate="slow" pitch="high">peanut</prosody>，再来一次，<prosody rate="slow" pitch="high">peanut</prosody>。<break time="500ms"/>
                另外，叶片闭合还能减少<emphasis>水分</emphasis>的蒸发。<break time="300ms"/>夜晚空气湿度大，如果叶片一直张开，水分就会大量跑掉。<break time="300ms"/>植物把叶片闭合起来，就像关上了一扇门，让水分乖乖留在身体里。<break time="300ms"/>“水分”的英文是“<say-as interpret-as="spell-out">water</say-as>”，跟我读，<prosody rate="slow" pitch="high">water</prosody>，再读一遍，<prosody rate="slow" pitch="high">water</prosody>。<break time="500ms"/>
                还有哦，有些植物的“睡眠”还和<emphasis>月光</emphasis>有关。<break time="300ms"/>月光有时候会影响植物的生长，植物通过“睡眠”来避免月光的过度照射。<break time="300ms"/>“月光”的英文是“<say-as interpret-as="spell-out">moonlight</say-as>”，大家读，<prosody rate="slow" pitch="high">moonlight</prosody>，再读一遍，<prosody rate="slow" pitch="high">moonlight</prosody>。<break time="700ms"/>
                现在小朋友们知道植物“睡眠”的作用啦。<break time="400ms"/>那我要问大家一个问题：<prosody pitch="high" rate="slow">如果把一种会“睡眠”的植物一直放在白天的环境里，它还会“睡觉”吗？</prosody><break time="800ms"/>
                好啦，今天关于植物“睡眠”的科普就到这里，<break time="300ms"/>希望你们以后能发现更多植物的小秘密哟。<prosody pitch="high" rate="slow">再见啦！</prosody>
                </speak>
                """;

        speakSsml(text, "zh-CN-XiaoyuMultilingualNeural", "zh-CN")
                .onSuccess(path -> log.info("file {}", path));
    }
}

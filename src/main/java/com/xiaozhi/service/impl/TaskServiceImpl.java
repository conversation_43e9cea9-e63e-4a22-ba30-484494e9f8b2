package com.xiaozhi.service.impl;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.dao.ManagerMapper;
import com.xiaozhi.dao.TaskInstanceMapper;
import com.xiaozhi.dao.TaskMetaMapper;
import com.xiaozhi.entity.Manager;
import com.xiaozhi.entity.TaskInstance;
import com.xiaozhi.entity.TaskMeta;
import com.xiaozhi.enums.TaskStatus;
import com.xiaozhi.service.TaskService;
import com.xiaozhi.utils.DateUtils;
import com.xiaozhi.vo.TaskCreateParams;
import com.xiaozhi.vo.TaskQueryParams;
import com.xiaozhi.vo.TaskUpdateParams;
import io.vavr.control.Either;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Service;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class TaskServiceImpl implements TaskService {

    @Resource
    private ManagerMapper managerMapper;

    @Resource
    private TaskMetaMapper taskMetaMapper;

    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final DateTimeFormatter TaskTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");

    @Override
    public List<?> findTasks(TaskQueryParams params) {
        var query = new OhMyLambdaQueryWrapper<TaskMeta>()
                .eq(TaskMeta::getManagerId, params.getManagerId())
                .select(TaskMeta::getId, TaskMeta::getType, TaskMeta::getWeekMask, TaskMeta::getTime, TaskMeta::getContent, TaskMeta::getDuration, TaskMeta::getIsEnabled);

        return taskMetaMapper.selectList(query);
    }

    @Override
    public Either<BizError, ?> createTask(TaskCreateParams params) {
        return null;
    }

    @Override
    public Either<BizError, ?> updateTask(Integer id, TaskUpdateParams params) {
        var query = new OhMyLambdaQueryWrapper<TaskMeta>()
                .eq(TaskMeta::getId, id);

        return Option.of(taskMetaMapper.selectOne(query))
                .toEither(BizError.ResourceNotFound)
                .filterOrElse(it -> {
                    // 禁用 直接跳过
                    if (params.getIsEnabled() != null && !params.getIsEnabled()) {
                        return true;
                    }
                    // 启用 检查是否有重复的
                    if (params.getIsEnabled() != null || it.getIsEnabled()) {
                        var countQuery = new OhMyLambdaQueryWrapper<TaskMeta>()
                                .eq(TaskMeta::getManagerId, it.getManagerId())
                                .eq(TaskMeta::getTime, Optional.ofNullable(params.getTime()).orElse(it.getTime()))
                                .eq(TaskMeta::getIsEnabled, true)
                                .ne(TaskMeta::getId, id);
                        if (params.getWeekMask() != null) {
                            countQuery.last(STR." AND week_mask & \{params.getWeekMask()} = \{params.getWeekMask()}");
                        } else {
                            countQuery.last(STR." AND week_mask & \{it.getWeekMask()} = \{it.getWeekMask()}");
                        }
                        return taskMetaMapper.selectCount(countQuery) <= 0;
                    }
                    return true;
                }, _ -> BizError.TaskConflicted)
                .peek(it -> {
                    if (params.getTime() != null) {
                        it.setTime(params.getTime());
                    }
                    if (params.getDuration() != null) {
                        it.setDuration(params.getDuration());
                    }
                    if (params.getWeekMask() != null) {
                        it.setWeekMask(params.getWeekMask());
                    }
                    if (params.getIsEnabled() != null) {
                        it.setIsEnabled(params.getIsEnabled());
                    }

                    it.setCronExpr(DateUtils.buildCron(it.getTime(), it.getWeekMask()));

                    taskMetaMapper.updateById(it);
                })
                .map(it -> {
                    var zone = ZoneId.of("Asia/Shanghai");
                    var inToday = LocalTime.parse(it.getTime()).atDate(LocalDate.now(zone)).atZone(zone);
                    var isFuture = inToday.isAfter(ZonedDateTime.now(zone));

                    // clean old task instance
                    var instanceQuery = new OhMyLambdaQueryWrapper<TaskInstance>()
                            .eq(TaskInstance::getMetaId, it.getId())
                            .select(TaskInstance::getId);
                    var instances = taskInstanceMapper.selectList(instanceQuery);
                    instances.forEach(ins -> stringRedisTemplate.opsForZSet().remove("xiaozhi:task:queue", ins.getId().toString()));
                    if (it.getIsEnabled() && isFuture) {
                        // create task instance
                        var bit = LocalDate.now().getDayOfWeek().getValue() % 7;
                        // 判断今天要不要跑
                        if ((it.getWeekMask() & (1 << bit)) == 0) return true;

                        // 计算下次执行时间
                        var fireTime = CronExpression.parse(it.getCronExpr()).next(LocalDateTime.now());
                        if (fireTime == null) return true;

                        var managerQuery = new OhMyLambdaQueryWrapper<Manager>()
                                .eq(Manager::getId, it.getManagerId())
                                .select(Manager::getId, Manager::getName, Manager::getCefr, Manager::getDeviceId);
                        var manager = managerMapper.selectOne(managerQuery);

                        // 创建任务实例
                        var instance = new TaskInstance()
                                .setType(it.getType())
                                .setContent(it.getContent())
                                .setDuration(it.getDuration())
                                .setMetaId(it.getId())
                                .setPlanFireTime(fireTime)
                                .setStatus(TaskStatus.Waiting)
                                .setDeviceId(manager.getDeviceId())
                                .setManagerId(manager.getId());
                        taskInstanceMapper.insert(instance);

                        var timestamp = fireTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
                        stringRedisTemplate.opsForZSet().add("xiaozhi:task:queue", instance.getId().toString(), timestamp);
                    }
                    return true;
                });
    }
}

package com.xiaozhi.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * 学习概览
 */
@Data
@Accessors(chain = true)
public class DailyStudyStatsVO {

    private String suggestion;

    /**
     * 较昨日增长数量
     */
    private int sentenceIncCount = 0;

    private int sentenceCount = 0;

    /**
     * 口语时长
     */
    private long speakingTime = 0;

    /**
     * 听力时长
     */
    private long listeningTime = 0;

    /**
     * 学习单词数量
     */
    private List<String> words = List.of();

    /**
     * 学习句子数量
     */
    private List<String> sentences = List.of();

    private List<String> topics = List.of();

    private List<String> grammars = List.of();

    /**
     * 每日词汇累加数量
     */
    private List<DailyVocabulary> vocabularies = List.of();

    public record DailyVocabulary(LocalDate day, int count) {}
}

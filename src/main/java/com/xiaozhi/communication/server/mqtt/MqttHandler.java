package com.xiaozhi.communication.server.mqtt;


import com.hivemq.client.mqtt.MqttClientState;
import com.hivemq.client.mqtt.datatypes.MqttQos;
import com.hivemq.client.mqtt.mqtt3.Mqtt3AsyncClient;
import com.hivemq.client.mqtt.mqtt3.message.publish.Mqtt3Publish;
import com.xiaozhi.communication.common.MessageHandler;
import com.xiaozhi.communication.common.SessionManager;
import com.xiaozhi.communication.domain.*;
import com.xiaozhi.communication.server.mqtt.udp.UdpServer;

import com.xiaozhi.communication.server.mqtt.udp.crypto.CryptoUtils;
import com.xiaozhi.config.ServerAddressConfig;
import com.xiaozhi.service.SysDeviceService;
import com.xiaozhi.utils.JsonUtil;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MQTT 消息处理器
 * 负责处理来自 EMQX 的消息，并路由到相应的处理器
 * 使用 HiveMQ MQTT Client
 */
@Slf4j
@Component
public class MqttHandler {

    @Autowired
    private MqttConfig mqttConfig;

    @Autowired
    private SessionManager sessionManager;

    @Autowired
    private MessageHandler messageHandler;

    @Autowired
    private SysDeviceService sysDeviceService;

    @Autowired
    private UdpServer udpServer;

    @Autowired
    private ServerAddressConfig serverAddressConfig;

    @Resource
    private Mqtt3AsyncClient mqttClient;

    // UDP连接ID到会话ID的映射
    private final ConcurrentHashMap<Integer, String> connectionIdToSessionMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        subscribe(serverAddressConfig.getMqtt().getServerSubscribeTopic());
        // 设置UDP服务器的数据包处理器
        udpServer.setPacketHandler(this::handleUdpPacket);
    }


    @PreDestroy
    public void destroy() {
        // 断开MQTT连接
        if (isConnected()) {
            mqttClient.disconnect().whenComplete((_, throwable) -> {
                if (throwable != null) {
                    log.error("断开 MQTT 连接失败", throwable);
                } else {
                    log.info("MQTT 连接已断开");
                }
            });
        }
    }

    /**
     * 订阅 Topic
     */
    private void subscribe(String topic) {
        try {
            mqttClient.subscribeWith()
                    .topicFilter(topic)
                    .qos(MqttQos.fromCode(mqttConfig.getTopic().getQos()))
                    .callback(this::handleMessage)
                    .send()
                    .whenComplete((subAck, throwable) -> {
                        if (throwable != null) {
                            log.error("订阅 Topic 失败: {}", topic, throwable);
                        } else {
                            log.info("订阅设备到服务器统一 Topic 成功: {}", topic);
                        }
                    });
        } catch (Exception e) {
            log.error("订阅 Topic 异常", e);
        }
    }

    /**
     * 处理接收到的 MQTT 消息
     */
    private void handleMessage(Mqtt3Publish publish) {
        var topic = publish.getTopic().toString();
        var payload = new String(publish.getPayloadAsBytes(), StandardCharsets.UTF_8);

        log.info("收到 MQTT 消息 - Topic: {}, Payload: {}", topic, payload);

        var wrappedPayload = JsonUtil.fromJson(payload, WrappedPayload.class);
        var deviceId = String.join(":", wrappedPayload.getClientId().split("@@@")[1].split("_"));

        try {
            if (wrappedPayload.getMessage() instanceof HelloMessage helloMessage) {
                handleHelloMessage(deviceId, wrappedPayload.getClientId(), helloMessage);
                return;
            }
            // 查找现有会话
            var session = findSessionByDeviceId(deviceId);
            if (!wrappedPayload.getMessage().getType().equals("task_action") && session == null) {
                log.warn("设备会话不存在，忽略消息 - DeviceId: {}, MessageType: {}", deviceId, wrappedPayload.getMessage().getType());
                return;
            }

            // 委托给统一的消息处理器
            messageHandler.handleMessage(wrappedPayload.getMessage(), session);
        } catch (Exception e) {
            log.error("处理 MQTT 消息失败 - Topic: {}", topic, e);
        }
    }

    /**
     * 处理 Hello 消息（创建会话）
     */
    private void handleHelloMessage(String deviceId, String clientId, HelloMessage helloMessage) {
        var device = sysDeviceService.findByDeviceId(deviceId);
        if (device == null) {
            log.warn("未知设备尝试连接 - DeviceId: {}", deviceId);
            return;
        }

        // 检查是否已存在会话
        var mqttSession = findSessionByDeviceId(deviceId);
        if (mqttSession != null) {
            log.info("设备已存在会话，关闭旧会话 - DeviceId: {}", deviceId);
            // sessionManager.closeSession(existingSession.getSessionId());
            // 创建新的 MQTT 会话，传入共享的 MQTT 连接
        }

        try {
            var sessionId = generateSessionId();
            mqttSession = new MqttSession(sessionId, deviceId, clientId, mqttClient, udpServer, this);

            log.info("设备 MQTT 会话创建成功 - DeviceId: {}, SessionId: {}", deviceId, mqttSession.getSessionId());

            var resp = new HelloMessageResp()
                    .setTransport("udp")
                    .setSessionId(mqttSession.getSessionId())
                    .setAudioParams(AudioParams.Opus)
                    .setUdp(
                            new UdpParams()
                                    .setServer(serverAddressConfig.getServerIp())
                                    .setPort(mqttConfig.getUdp().getPort())
                                    .setEncryption("aes-128-ctr")
                                    .setKey(mqttSession.getUdpInfo().getKeyHex())
                                    .setNonce(mqttSession.getUdpInfo().getNonceHex())
                    );

            mqttSession.sendTextMessage(JsonUtil.toJson(resp));

            // 这会处理设备注册、角色初始化、工具注册等所有连接后的初始化工作
            messageHandler.afterConnection(mqttSession, deviceId);
        } catch (Exception e) {
            log.error("处理 Hello 消息失败 - DeviceId: {}", deviceId, e);
        }
    }

    /**
     * 根据设备ID查找会话
     */
    private MqttSession findSessionByDeviceId(String deviceId) {
        var session = sessionManager.getSessionByDeviceId(deviceId);
        if (session instanceof MqttSession) {
            return (MqttSession) session;
        }
        return null;
    }

    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return UUID.randomUUID().toString();
    }

    /**
     * 处理收到的 UDP 数据包
     */
    public void handleUdpPacket(byte[] data, InetSocketAddress senderAddress) {
        // log.info("Receive UDP packet {}", senderAddress.getHostString());
        try {
            if (data.length < 16) { // 最小包大小：16 字节
                log.warn("收到无效的 UDP 数据包，长度太短: {} 字节", data.length);
                return;
            }

            byte[] nonce = new byte[16];
            System.arraycopy(data, 0, nonce, 0, 16);

            // 解析数据包头部 0-16 byte
            var buffer = ByteBuffer.wrap(data);
            buffer.order(ByteOrder.BIG_ENDIAN); // 设置为大端序

            // 读取版本号 1 byte
            byte version = buffer.get();
            if (version != 1) {
                log.warn("不支持的 UDP 数据包版本: {}", version);
                return;
            }

            // skip zero
            buffer.get();

            // 读取数据长度 2 byte
            var dataLength = buffer.getShort();

            // 读取连接ID 4 byte
            var connectionId = buffer.getInt();

            // 读取时间戳 4 byte
            var timestamp = buffer.getInt();

            // 读取序列号 4 byte
            var sequence = buffer.getInt();

            // 读取加密的音频数据
            var encryptedData = new byte[data.length - 16];
            System.arraycopy(data, 16, encryptedData, 0, encryptedData.length);

            log.debug("解析 UDP 数据包 - ConnectionId: {}, Sequence: {}, DataLength: {}, ActualLength: {}, Timestamp: {}, NonceHex: {}",
                    connectionId, sequence, dataLength, encryptedData.length, timestamp, CryptoUtils.bytesToHex(nonce));

            // 通过连接ID查找对应的会话
            var sessionId = connectionIdToSessionMap.get(connectionId);
            if (sessionId == null) {
                log.warn("未找到对应连接ID的会话 - ConnectionId: {}", connectionId);
                return;
            }

            // 获取会话
            var chatSession = sessionManager.getSession(sessionId);
            if (chatSession == null) {
                log.warn("会话不存在 - SessionId: {}", sessionId);
                return;
            }

            // 获取MQTT会话以访问UDP信息
            if (!(chatSession instanceof MqttSession mqttSession)) {
                log.warn("会话不是MQTT会话 - SessionId: {}", sessionId);
                return;
            }

            var udpInfo = mqttSession.getUdpInfo();
            if (udpInfo == null) {
                log.warn("UDP信息不存在 - SessionId: {}", sessionId);
                return;
            }

            // 设置客户端地址
            if (udpInfo.getClientAddress() == null) {
                udpInfo.setClientAddress(senderAddress);
            }

            // 验证序列号（防重放攻击）
            //if (!udpInfo.validateSequence(sequence)) {
            //    log.warn("序列号验证失败 - SessionId: {}, Sequence: {}", sessionId, sequence);
            //    return;
            //}

            // 使用数据包中的nonce进行解密，而不是会话中存储的nonce
            var audioData = CryptoUtils.decrypt(encryptedData, udpInfo.getKey(), nonce, sequence);
            if (audioData == null) {
                log.error("音频数据解密失败 - SessionId: {}", sessionId);
                return;
            }

            log.debug("音频数据解密成功 - SessionId: {}, AudioDataLength: {}", sessionId, audioData.length);

            messageHandler.handleBinaryMessage(sessionId, audioData);
        } catch (Exception e) {
            log.error("处理 UDP 数据包时发生错误 - SenderAddress: {}", senderAddress, e);
        }
    }

    /**
     * 注册连接ID到会话ID的映射
     */
    public void registerConnectionId(int connectionId, String sessionId) {
        connectionIdToSessionMap.put(connectionId, sessionId);
        log.debug("注册连接ID映射 - ConnectionId: {}, SessionId: {}", connectionId, sessionId);
    }

    /**
     * 取消连接ID到会话ID的映射
     */
    public void unregisterConnectionId(int connectionId) {
        String sessionId = connectionIdToSessionMap.remove(connectionId);
        if (sessionId != null) {
            log.debug("取消连接ID映射 - ConnectionId: {}, SessionId: {}", connectionId, sessionId);
        }
    }

    /**
     * 检查 MQTT 连接状态
     *
     * @return 是否已连接
     */
    public boolean isConnected() {
        return mqttClient != null && mqttClient.getState() == MqttClientState.CONNECTED;
    }

    @Data
    public static class WrappedPayload {
        private String clientId;
        private Message message;
    }

}

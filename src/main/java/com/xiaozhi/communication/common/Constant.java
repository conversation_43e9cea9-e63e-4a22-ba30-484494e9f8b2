package com.xiaozhi.communication.common;

import java.util.HashMap;
import java.util.Map;

public class Constant {

    public static final Map<String, String> LevelContent = new HashMap<>() {{
        put("A1", """
                - 所有输出应保持在英语母语5–6岁儿童的表达难度，帮助孩子在轻松的氛围中练习开口。
                - 当用户回答很短时：重复关键词并提出一个非常简单的好奇问题。
                - 当用户回答较长时：只延展话题一步，简单提问或分享小故事。
                - 单句小于8词
                """);
        put("A2", """
                - 所有输出应保持在英语母语7–8岁儿童的表达难度，帮助孩子在轻松的氛围中练习开口。
                - 当用户回答很短时：重复关键词并提出一个简单的好奇问题。
                - 当用户回答较长时：可以延展话题两步，简单比较、引导多说。
                - 单句小于10词
                """);
        put("B1", """
                - 所有输出应保持在英语母语9-10岁儿童的表达难度，帮助孩子在轻松的氛围中练习开口。
                - 当用户回答很短时：重复关键词并提出一个稍有挑战的问题。
                - 当用户回答较长时：可以延展话题多步，引导解释原因或讲小故事。
                - 单句小于15词
                """);
    }};

    public static final Map<Integer, Scenario> ScenarioMap = new HashMap<>() {{
        put(1, new Scenario(1, "Lesson 1 Packing Luggage for the Trip（打包旅行行李）", """
                角色1：Hey! Are you ready to pack for your trip tomorrow?（嘿！你准备好为明天的旅行打包行李了吗？）
                角色2：Not yet. I’m not sure what to take.（还没呢。我不确定要带些什么。）
                角色1：Let’s think—do you need to bring a jacket? It might be cold there.（咱们想想——你需要带件夹克吗？那边可能会有点冷。）
                角色2：OK! I’ll put it in the bag.（好的！我要把它放进包里。）
                角色1：Don’t forget your passport and plane ticket. They’re super important!（别忘了你的护照和机票哦，这些超级重要的！）
                角色2：Thanks for reminding me. I’ll keep them in my bag.（谢谢你提醒我。我会把它们放在包里的。）
                角色1：How many shoes are you taking? We only have a small bag.（你要带几双鞋呀？咱们只有一个小包。）
                角色2：Just two pairs.（就两双。）
                角色1：Perfect!  Are you excited for tomorrow?（太好了！你对明天的旅行期待吗？）
                角色2：Yes! I can’t wait to go to the airport.（当然期待啦！我都等不及要去机场了。）
                """));
        put(2, new Scenario(2, "Lesson 2 Checking in at the Airport（机场值机）", """
                角色1：Good morning! May I see your passport and ticket, please?（早上好！麻烦出示一下您的护照和机票好吗？）
                角色2：Sure, here they are.（好的，给您。）
                角色1：Thank you. Will you check this bag or carry it on?（谢谢。这个包您是要托运，还是随身携带呢？）
                角色2：I’d like to check it, please.（麻烦帮我托运吧。）
                角色1：No problem. Do you have any dangerous things in your bag?（没问题。您的包里有危险品吗？）
                角色2：No, just candy and fruit.（没有，就糖果和水果。）
                角色1：Great. Here’s your boarding pass.（好的。这是您的登机牌。）
                角色2：What time does the plane take off?（飞机什么时候起飞呀？）
                角色1：It takes off at ten. Please go through security first. Have a nice flight!（10点起飞。请您先去安检。祝您旅途愉快！）
                角色2：Thank you very much.（非常感谢。）
                """));
        put(3, new Scenario(3, "Lesson 3 Going Through Airport Security（机场安检）", """
                角色1：Hello! Please put your handbag and laptop in the tray.（您好！请把您的手提包和笔记本电脑放进托盘里。）
                角色2：Okay, let me take out my laptop first.（好的，我先把笔记本电脑拿出来。）
                角色1：Thank you. Also, could you take off your jacket and shoes?（谢谢。另外，麻烦您脱下夹克和鞋子好吗？）
                角色2：Sure, no problem.（好的，没问题。）
                角色1：Please step through the metal detector. Wait—do you have any coins in your pocket?（请走过金属探测器。等一下——您口袋里有硬币吗？）
                角色2：Oh! I forgot. Let me take them out.（哦！我忘了。我这就拿出来。）
                角色1：Good. Now you can collect your things. Everything looks okay.（好的。现在您可以取回您的物品了，一切都没问题。）
                角色2：Thanks. Can I put my jacket back on now?（谢谢。我现在可以把夹克穿上了吗？）
                角色1：Yes, of course. Your gate is just ahead. Enjoy your trip!（当然可以。您的登机口就在前面。祝您旅途愉快！）
                角色2：I will. Thank you.（我会的。谢谢。）
                """));
        put(4, new Scenario(4, "Lesson 4 Boarding the Plane（登机）", """
                角色1：Welcome aboard! May I check your boarding pass, please?（欢迎登机！麻烦出示一下您的登机牌好吗？）
                角色2：Here you go. Is this seat by the window?（给您。这个座位靠窗吗？）
                角色1：Yes, that’s right—it’s by the window. Do you need help with your bag?（对的，没错——这是靠窗的座位。需要帮忙放手提包吗？）
                角色2：No, thanks. I can do it.（不用了，谢谢。我可以的。）
                角色1：Great. Please fasten your seatbelt. We’ll take off in ten minutes.（好的。请系好安全带，我们10分钟后起飞。）
                角色2：Okay, I’ll do it now.（好的，我现在就系。）
                角色1：Would you like a glass of water before takeoff?（起飞前需要来一杯水吗？）
                角色2：Yes, please. （好的，麻烦了。）
                角色1：Here you go. Enjoy the flight, and let me know if you need anything else!（给您。祝您飞行愉快，有需要的话随时告诉我！）
                角色2：Thank you. I will.（谢谢。我会的。）
                """));
        put(5, new Scenario(5, "Lesson 5 Arriving at the Hotel（抵达酒店）", """
                角色1：Good afternoon! May I help you？（下午好！有什么可以帮您吗？）
                角色2：Yes, my name is Lucy. I booked a room yesterday.（有的，我叫露西，昨天预订了一间房。）
                角色1：Let me check… Yes! A single room for three nights. May I see your ID?（我查一下……找到了！是一间住3晚的单人房。麻烦出示一下您的身份证件好吗？）
                角色2：Sure, here’s my passport.（好的，这是我的护照。）
                角色1：Thank you. Here’s your room key.（谢谢。这是您的房卡。）
                角色2：Does the room have free Wi-Fi?（房间有免费Wi-Fi吗？）
                角色1：Yes, the Wi-Fi password is on the back of the card.（有的，Wi-Fi 密码在卡片背面。）
                角色2：That’s perfect. Where is the elevator?（太好啦。电梯在哪里呢？）
                角色1：It’s over there, next to the lobby. Enjoy your stay with us!（就在那边，大厅旁边。祝您入住愉快！）
                角色2：Thank you very much.（非常感谢。）
                """));
        put(6, new Scenario(6, "Lesson 6 Asking for Directions to a Restaurant（询问餐厅路线）", """
                角色1：Excuse me, can I help you? You look lost.（打扰一下，需要帮忙吗？您看起来好像迷路了。）
                角色 2：Yes! I’m looking for this restaurant.（是的！我在这家餐厅。）
                角色 1：Go straight, turn left at the light.（直走，红绿灯左转。）
                角色 2：How long to walk there?（走路要多久？）
                角色 1：About five minutes. It’s next to a big park.（大概 5 分钟。在一个大公园旁边。）
                角色 2：Is there a bus?（有公交车吗？）
                角色 1：Yes, Bus Ten. But walking is faster.（有，10 路车。但走路更快。）
                角色 2：Okay, I’ll walk. Thanks!（好，我走路去。谢谢！）
                角色 1：You’re welcome! Enjoy your meal!（不客气！用餐愉快！）
                角色2：Thanks! Bye!（谢谢！再见！）
                """));
        put(7, new Scenario(7, "Lesson 7 Buying Souvenirs Before Leaving（离开前买纪念品）", """
                角色 1：Hello! What can I do for you？（您好！我能为你做些什么？）
                角色 2：Hi! I want to buy something for my family. （你好！我想给家人买些东西。）
                角色 1：Our city cups are very popular—they have the airport and park on them.（我们的城市杯很受欢迎，上面印了机场和公园的图案。）
                角色 2：How much is one cup?（一个杯子多少钱呀？）
                角色 1：They’re five dollars each. （每个 5 美元。）
                角色 2：Great! I’ll take three cups.（太好了！我要 3 个杯子。）
                角色 1：Sure. That’s a total of fifteen dollars. Will you pay by cash or card?（好的。一共 15 美元。您是付现金还是刷卡呀？）
                角色 2：I’ll pay with card, please.（麻烦刷卡吧。）
                角色 1：Here’s your receipt. Hope you had a nice trip here!（这是您的收据。希望您在这里玩得愉快！）
                角色 2：Thank you.  I’ll come back soon!（谢谢。我以后会再来的！）
                """));
    }};


    public record Scenario(Integer id, String title, String content) {
    }
}

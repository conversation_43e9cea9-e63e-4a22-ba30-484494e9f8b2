package com.xiaozhi.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Session 关闭请求事件
 */
@Getter
public class SessionCloseRequestEvent extends ApplicationEvent {
    private final String sessionId;
    private final String reason;

    public SessionCloseRequestEvent(Object source, String sessionId, String reason) {
        super(source);
        this.sessionId = sessionId;
        this.reason = reason;
    }

}

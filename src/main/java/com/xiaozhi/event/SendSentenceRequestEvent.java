package com.xiaozhi.event;

import com.xiaozhi.communication.common.ChatSession;
import org.springframework.context.ApplicationEvent;

/**
 * 发送句子请求事件
 * 用于解决SessionManager和DialogueService之间的循环依赖
 */
public class SendSentenceRequestEvent extends ApplicationEvent {
    private final ChatSession session;
    private final String text;
    private final boolean isLast;
    private final Runnable callback;

    public SendSentenceRequestEvent(Object source, ChatSession session, String text, boolean isLast, Runnable callback) {
        super(source);
        this.session = session;
        this.text = text;
        this.isLast = isLast;
        this.callback = callback;
    }

    public SendSentenceRequestEvent(Object source, ChatSession session, String text, boolean isLast) {
        this(source, session, text, isLast, null);
    }

    public ChatSession getSession() {
        return session;
    }

    public String getText() {
        return text;
    }

    public boolean isLast() {
        return isLast;
    }

    public Runnable getCallback() {
        return callback;
    }
}

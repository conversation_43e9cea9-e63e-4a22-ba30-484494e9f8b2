package com.xiaozhi.controller.device;

import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.interceptor.QueryParam;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.service.DailyStudyStatsService;
import com.xiaozhi.service.MediaService;
import com.xiaozhi.vo.MediaQueryParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/d")
@Tag(name = "设备端接口", description = "设备端接口")
public class DeviceSupportController {

    @Resource
    private MediaService mediaService;

    @Resource
    private DailyStudyStatsService dailyStudyStatsService;

    @PutMapping("/stats/listening")
    @Operation(summary = "更新听力时长")
    public Either<BizError, ?> updateListeningTime(@Authorized AuthorizedUser user) {
        return dailyStudyStatsService.updateListeningTime(user.getId());
    }

    @GetMapping("/medias")
    @Operation(summary = "获取媒体列表")
    public Resp find(@Authorized AuthorizedUser user, @QueryParam MediaQueryParams params) {
        return mediaService.find4device(params);
    }

    @GetMapping("/scenarios")
    @Operation(summary = "")
    public Resp findScenarios() {
        return Resp.succeed(List.of());
    }

}

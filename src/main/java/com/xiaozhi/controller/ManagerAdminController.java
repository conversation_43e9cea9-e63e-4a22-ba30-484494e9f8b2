package com.xiaozhi.controller;

import com.xiaozhi.common.interceptor.QueryParam;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.service.ManagerService;
import com.xiaozhi.vo.ManagerQueryParams;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("managers")
public class ManagerAdminController {

    @Resource
    private ManagerService managerService;


    @GetMapping
    public Resp find(@QueryParam ManagerQueryParams params) {
        return  managerService.findPage(params);
    }

}

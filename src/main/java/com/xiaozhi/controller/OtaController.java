package com.xiaozhi.controller;

import com.xiaozhi.common.interceptor.PassAuth;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.config.ServerAddressConfig;
import com.xiaozhi.entity.SysDevice;
import com.xiaozhi.service.ManagerService;
import com.xiaozhi.service.SysDeviceService;
import com.xiaozhi.service.factory.ActivationFactory;
import com.xiaozhi.utils.CmsUtils;
import com.xiaozhi.utils.JsonUtil;
import com.xiaozhi.vo.OtaRequest;
import com.xiaozhi.vo.OtaResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@Slf4j
@RestController
@RequestMapping("/ota")
public class OtaController {

    private static String LATEST_VERSION = "1.9.4";
    private static String LATEST_URL = "https://mytikas-testing.oss-cn-beijing.aliyuncs.com/xiaozhi/xiaozhi.bin";

    @Resource
    private SysDeviceService deviceService;

    @Resource
    private ManagerService managerService;

    @Resource
    private ActivationFactory activationFactory;

    @Resource
    private ServerAddressConfig serverAddressConfig;

    @PassAuth
    @PostMapping()
    @Operation(summary = "OTA接口")
    public ResponseEntity<String> ota(@RequestHeader("Device-Id") String deviceId, @RequestBody OtaRequest params) {

        // 获取设备ID (MAC地址)
        if (deviceId == null) {
            deviceId = params.getMacAddress();
        }

        if (deviceId == null || !CmsUtils.isMacAddressValid(deviceId)) {
            return new ResponseEntity<>("{\"error\": \"设备ID不正确\"}", HttpStatus.BAD_REQUEST);
        }

        // 通过 DeviceId/MacAddress 查询设备
        var device = deviceService.findByDeviceId(deviceId);

        var resp = new OtaResponse()
                .setServerTime(OtaResponse.ServerTime.CST())
                .setFirmware(new OtaResponse.Firmware(LATEST_VERSION, params.getBoard().getType().equals("echoear") ? LATEST_URL : ""));

        if (device == null) {
            var code = activationFactory.getService().generateActiveCode(deviceId, params.getBoard().getType());
            var message = String.format("请在小智管理后台输入激活码 %s 添加设备", code);
            resp.setActivation(new OtaResponse.Activation(code, message, deviceId));
            log.info("设备 {} 未绑定，生成激活码: {}", deviceId, code);
        } else if (managerService.findBy(deviceId) == null) {
            resp.setBinding(new OtaResponse.Binding("请先登录家长端微信小程序", true));
        } else {
            resp.setWebsocket(new OtaResponse.Websocket(serverAddressConfig.getWebsocketAddress(), ""));
            var mqtt = new OtaResponse.Mqtt()
                    .setEndpoint(serverAddressConfig.getMqttAddress())
                    .setUsername(serverAddressConfig.getMqtt().getUsername())
                    .setPassword(serverAddressConfig.getMqtt().getPassword())
                    .setClientId("GID@@@%s".formatted(deviceId.replaceAll(":", "_")))
                    // .setClientId("GID@@@%s@@@%s".formatted(deviceId.replaceAll(":", "_"), UUID.randomUUID()))
                    .setPublishTopic(serverAddressConfig.getMqtt().getClientPublishTopic());
            resp.setMqtt(mqtt);

            // 更新在线状态&最后登录时间
            var update = new SysDevice()
                    .setIsOnline(true)
                    .setLastLoginTime(new Date())
                    .setVersion(params.getApplication().getVersion());
            deviceService.update(device.getId(), update);
        }

        return new ResponseEntity<>(JsonUtil.toJson(resp), HttpStatus.OK);
    }

    @PassAuth
    @PostMapping("/activate")
    @Operation(summary = "查询设备激活结果")
    public ResponseEntity<?> activate(@Parameter(name = "Device-Id", description = "设备唯一标识", required = true, in = ParameterIn.HEADER)
                                      @RequestHeader("Device-Id") String deviceId) {
        if (!CmsUtils.isMacAddressValid(deviceId)) {
            return new ResponseEntity<>(HttpStatus.ACCEPTED);
        }

        var device = deviceService.findByDeviceId(deviceId);
        if (device == null) {
            return new ResponseEntity<>(HttpStatus.ACCEPTED);
        }

        log.info("OTA激活结果查询成功, deviceId: {} 激活时间: {}", deviceId, device.getCreatedAt());
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PassAuth
    @PostMapping("/binding")
    @Operation(summary = "查询设备激活结果")
    public ResponseEntity<?> binding(@Parameter(name = "Device-Id", description = "设备唯一标识", required = true, in = ParameterIn.HEADER)
                                     @RequestHeader("Device-Id") String deviceId) {
        if (!CmsUtils.isMacAddressValid(deviceId)) {
            return new ResponseEntity<>(HttpStatus.ACCEPTED);
        }

        var manager = managerService.findBy(deviceId);
        if (manager == null) {
            return new ResponseEntity<>(HttpStatus.ACCEPTED);
        }

        log.info("OTA绑定结果查询成功, deviceId: {}", deviceId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PutMapping("version")
    @Operation(summary = "更新固件版本和地址")
    public Resp updateVersion(@RequestBody PublishParams params) {
        LATEST_URL = params.url;
        LATEST_VERSION = params.version;
        return Resp.succeed("success");
    }

    public record PublishParams(String version, String url) {
    }
}

package com.xiaozhi.controller;

import com.xiaozhi.common.interceptor.QueryParam;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.service.MediaService;
import com.xiaozhi.vo.MediaCreateParams;
import com.xiaozhi.vo.MediaGenerationParams;
import com.xiaozhi.vo.MediaQueryParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("medias")
@Tag(name = "媒体资源管理", description = "媒体资源管理")
public class MediaController {

    @Resource
    private MediaService mediaService;

    @GetMapping
    @Operation(summary = "获取媒体列表")
    public Resp find(@QueryParam MediaQueryParams params) {
        return mediaService.findPage(params);
    }

    @PostMapping
    @Operation(summary = "创建媒体资源")
    public Either<BizError, ?> create(@RequestBody MediaCreateParams params) {
        return mediaService.create(params);
    }

    @DeleteMapping("{id}")
    @Operation(summary = "删除媒体资源")
    public Either<BizError, ?> delete(@PathVariable Integer id) {
        return mediaService.delete(id);
    }

    @PostMapping("gen")
    @Operation(summary = "生成媒体资源")
    public Either<BizError, ?> generate(@RequestBody MediaGenerationParams params) {
        return mediaService.generate(params);
    }

}

* AI 英语
** TODO 首屏 [4/7]
- [X] 眼睛根据不同的状态触发不同的动画
- [X] 眼睛页面轻触屏幕显示状态栏
- [ ] 左右滑动进入菜单页面 （滑动或长按进入滑动）
- [X] 唤醒时、说话时 先触发提示音再播放内容
- [X] 聆听中点击返回待机状态、说话中点击中断说话返回聆听中
- [ ] 非首屏唤起聊天时，通过浮层展现对话内容 (浮层可能问题；替代方案 返回首屏）
- [ ] 对话中通过字幕按钮切换眼睛表情和聊天记录（实现问题）

** TODO 设置 [4/4]
- [X] 声音设置
- [X] 睡眠模式
- [X] 设备信息
- [X] 系统信息

** TODO 听力内容 [2/4]
- [ ] 播放列表: 可根据分类展示
- [X] 基础播放控制: 播放、暂停、切歌
- [X] 全局播放控制: 播放任意资源时出现 (暂停 1 分钟后消失)
- [ ] 流媒体资源


#+BEGIN_SRC restclient
播放列表接口
GET https://rdmytikasapitest.lezhilong.cn/xiaozhi-esp32-server/api/v1/d/medias
Authorization: 74:4d:bd:7f:2f:98

#
{
  "data": [
    {
      "title": "三只老虎", # 音频名称
      "cover": "http://domain.com/tiger.png", # 封面图
      "url": "http://domain.com/tiger.wav" # 音频地址
    }
  ]
}
#+END_SRC

** TODO 待评估 [0/6]
- [ ] 播放器：采用本地播放，通过定时拉取音频并保存到SD卡中
- [ ] 支持动画表情与聊天记录切换
- [ ] 在非对话页面唤醒时跳转到对话页面
- [ ] 触摸相关移植
- [ ] 全局播放控制
- [ ] 音频歌词显示（将歌词嵌入音频中）

** IN-PROGRESS API [7/13]
- [X] 定时生成 task instance
- [X] 开启、关闭 meta 时，处理 instance；同一时刻不能有多个任务
- [X] 执行 task
- [X] 计算 task content 执行时间，切换任务内容
- [X] 主动推送测试
- [X] 口语任务的内容存入聊天记录
- [X] 家长端主动推送的内容存入聊天记录
- [ ] Summary测试
- [ ] 更新 TaskInstance 完成状态
- [ ] Azure 替换
- [ ] 流式 TTS
- [ ] intent 优化
- [ ] 稳定性优化

** IN-PROGRESS rk [0/6]
- [ ] ling 是如何实现的
- [ ] 安卓开发流程： 直接开发 APP，然后板子上测试？
- [ ] APP 能否跟 硬件并行
- [ ] 开发板选型： 手机芯片 vs rk
- [ ] 定制硬件：麦克风阵列、喇叭, 能否独立完成 或 接入开发
- [ ] 学习机建议： 起步最快，成熟稳定
  

* TODO MC records
- [X] 凛冬乄零点天亮
- [X] 雨寻道
- [X] 小幺幺
- [X] 打杂的小九
- [X] 凛冬乄清旺来
- [X] 木木木
- [X] 汝妻吾养汝毋虑
- [X] 诗情沉墨
- [X] 天地合
- [X] 无聊崛起

- [X] 凛冬乄零点凌晨
- [X] 九五二七
- [X] 大都督
- [X] 展雄飞
- [X] 农夫三拳
- [X] 零点黄昏
- [X] 好运来

- [X] 消言君
- [X] 初冬落下的雪
- [X] 布噜噜
- [X] 一怒为红颜
- [X] 凛冬乄金顺
- [X] 烟雨秀才
- [X] 凛冬乄小气的瑶瑶

- [X] 泡泡玛特
- [X] 烧鸡
- [X] 王剑仙
- [X] 大雄
- [X] 老虎
- [X] 范小闹
- [X] 一怒为红颜

- [X] 樱花树下谁也浪漫
- [X] 一怒为红颜
- [X] 零点黄昏
- [X] 凤铃山
- [X] 风吹半夏微凉
- [X] 只因你
- [X] 病树枝头又逢春

- [X] 小璃沫
- [ ] 凛冬乄达科哦豁
- [ ] 白驹过隙
- [ ] 琉月清荷
- [ ] 秋秋马 
- [ ] 凛冬乄严伦伦
- [ ] 爱像是一场小雨


- [ ] 凛冬乄江辰666
- [ ] 凛冬乄九哥

- [ ] 流氓东叔
- [ ] 金爆爆
- [ ] 凛冬乄随风而去
- [ ] 凛春乄陌上人如玉
- [ ] 凛春乄爱妃领旨
- [ ] 台风好大
- [ ] 凛春乄夏商周

- [ ] 凛春乄林海蓝猫
  
- [ ] 西红柿
- [ ] 凛冬乄巅峰且温柔
- [ ] 无聊玩一下
- [ ] 星星躲进月亮取暖
- [ ] 蔡徐坤不爱打篮球
- [ ] 凛冬乄随风而去
- [ ] 凛冬乄大白猫
- [ ] 凛冬乄无天
- [ ] 凛冬乄风中散
- [ ] 九千七
- [ ] 碳水核蛋鸭
- [ ] 柳扶风
- [ ] 六便士
- [ ] 巷闻酒香
- [ ] 凛冬乄鱼快不吃鱼
- [ ] 曜世丨宝妈
- [ ] 花弄影

- [ ] 冰冰冰
- [ ] 寒川定天下
- [ ] 曜世丨小宝贝


* COST

| 候选方案                            | 准确率 (WER) | 延迟   | 价格          | 结论     |
| --------------------------------   | ---------   | ------ | ------------  | -----    |
| **FunASR-paraformer-large** (本地) | 3.8 %       | 80 ms  | 0 元          | ✅ 采用  |
| Azure 语音                         | 4.1 %       | 120 ms | 0.006 元/15 s | 备选     |
| Whisper-Large-v3                   | 3.5 %       | 600 ms | 0.004 元/15 s | 太重，弃用 |


* PRD

| 模块 | 功能描述              | 预计工时 |
|------+----------------------+------|
| 注册 | 注册用户、绑定设备      |    4 |
| 首页 | 报告统计(不含设备端数据) |    4 |
| 任务 | 任务创建、执行         |    8 |
| 设置 | 个人信息、对话设置      |    4 |
| 上传 | 文件管理、OCR、TTS     |   16 |
| 合计 |                      |   36 |

# #+TBLFM: $3=@2$3+@3$3+@4$3+@5$3+@6$3+@7$3

| 模块     | 功能描述                        | 预计工时 |
|----------+--------------------------------+---------|
| 注册绑定  | 注册、重置、目标设置               |       8 |
| 登录 | google、apple等三方登录、email 登录 |      16-24 |
| 首页统计  | 今日数据、日历数据、设备解绑        |       8 |
| 任务管理  | 任务管理、自定义触发、稍后提醒       |       8 |
| 上传     | 文件管理、文件提取/生成音频、文件状态 |       8 |
| 设置     | 个人信息、对话设置                |       4 |


